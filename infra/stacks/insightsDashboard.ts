import { PeekApp, PeekStack, PeekStaticWebsiteDeployment } from "@piiqtechnologies/peek-iac";

export class InsightsDashboardDeploymentStack extends PeekStack {
  constructor(app: PeekApp) {
    super(app, { name: "insightsDashboardFrontendStaticWebsite" });

    const isProd = this.isProd();
    const alternativeDomainNames = [];

    if (isProd) {
      alternativeDomainNames.push({
        domain: "insights.peek.us",
        createCname: false,
      });
    }

    new PeekStaticWebsiteDeployment(this, {
      name: "insights-dashboard",
      subdomain: "insights",
      alternativeDomainNames,
      sourcePath: "apps-web/insights-dashboard/build",
    });
  }
}
