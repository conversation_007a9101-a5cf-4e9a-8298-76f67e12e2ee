#!/usr/bin/env node
import { loadContext, PeekApp, printResources } from "@piiqtechnologies/peek-iac";
import { DashboardDeploymentStack } from "./stacks/dashboard";
import { SgtDeploymentStack } from "./stacks/sgt";
import { SideloaderDeploymentStack } from "./stacks/sideloader";
import { VirtualTourDeploymentStack } from "./stacks/virtualTour";
import { InsightsDashboardDeploymentStack } from "./stacks/insightsDashboard";

const frontendApp = new PeekApp({ outdir: "cdk.out" });
const args = process.argv.slice(3);
loadContext(frontendApp, args);

const project = process.env.PROJECT_NAME;

if (!project || project.toUpperCase() === "DASHBOARD") buildDashboardInfra();
if (!project || project.toUpperCase() === "SELF-GUIDED-TOUR") buildSgtInfra();
if (!project || project.toUpperCase() === "SIDELOADER") buildSideLoaderInfra();
if (!project || project.toUpperCase() === "VIRTUAL-TOUR") buildVirtualTourInfra();
if (!project || project.toUpperCase() === "INSIGHTS-DASHBOARD") buildInsightsDashboardInfra();

const assembly = frontendApp.synth();
printResources(assembly);

function buildDashboardInfra() {
  new DashboardDeploymentStack(frontendApp);
}

function buildSgtInfra() {
  new SgtDeploymentStack(frontendApp);
}

function buildSideLoaderInfra() {
  new SideloaderDeploymentStack(frontendApp);
}

function buildVirtualTourInfra() {
  new VirtualTourDeploymentStack(frontendApp);
}

function buildInsightsDashboardInfra() {
  new InsightsDashboardDeploymentStack(frontendApp);
}
