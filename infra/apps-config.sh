#!/bin/bash

# Define an associative array to map project paths to their respective .env locations
declare -A PROJECTS_MAP=(
  ["common"]=".env"
  ["docs/storybook"]="./apps-docs/storybook/.env"
  ["embed/casa-widget"]="./apps-embed/casa-widget/.env"
  ["embed/chrome-extension"]="./apps-embed/chrome-extension/.env"
  ["web/dashboard"]="./apps-web/dashboard/.env"
  ["web/sideloader"]="./apps-web/sideloader/.env"
  ["web/self-guided-tour"]="./apps-web/self-guided-tour/.env"
  ["web/virtual-tour"]="./apps-web/virtual-tour/.env"
  ["web/insights-dashboard"]="./apps-web/insights-dashboard/.env"
)

