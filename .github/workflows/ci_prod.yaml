name: Production CI

on:
  push:
    tags:
      - "PROD-*"

jobs:
  synth:
    name: Synth
    runs-on: ubuntu-latest
    environment: prod
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Extract build target from tag
        id: extract
        env:
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: |
          TAG_NAME="${GITHUB_REF_NAME#PROD-}"
          # Extract the project name from the tag
          PROJECT_NAME=$(echo "$TAG_NAME" | sed -E 's/^[0-9]+\.[0-9]+\.[0-9]+-//')        
          # Convert project name to lowercase
          PROJECT_NAME_LOWER=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]')
          case "$PROJECT_NAME_LOWER" in
            "sideloader" | "self-guided-tour" | "virtual-tour" | "dashboard" | "casa-widget" | "insights-dashboard")
              echo "Valid Project: $PROJECT_NAME_LOWER"
              echo "PROJECT_NAME=$PROJECT_NAME_LOWER" >> $GITHUB_ENV
              ;;
            *)
              echo "Invalid Project: $PROJECT_NAME"
              echo "Valid Project filter are: SIDELOADER, SELF-GUIDED-TOUR, VIRTUAL-TOUR, DASHBOARD, CASA-WIDGET, INSIGHTS-DASHBOARD"
              exit 1
              ;;
          esac

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Installing Dependencies
        uses: peek-tech/tools-infra-actions/.github/actions/install@main
        with:
          cache_key: "node_modules"
          npm_token: ${{ secrets.NPM_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Create .env file
        env:
          ENVIRONMENT: prod
          PROJECT_NAME: ${{ env.PROJECT_NAME }}
        run: |
          yarn update:env:common
          yarn update:env --filter ${{ env.PROJECT_NAME }}

      - name: Build
        env:
          PROJECT_NAME: ${{ env.PROJECT_NAME }}
        run: yarn build:${{ env.PROJECT_NAME }}

      - name: "Configure AWS Credentials Prod Account"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ vars.AWS_ROLE_TO_ASSUME }}

      - name: Yarn synth
        env:
          PROJECT_NAME: ${{ env.PROJECT_NAME }}
        uses: peek-tech/tools-infra-actions/.github/actions/synth@main
        with:
          dependencies_cache_key: "frontend_node_modules"
          cdk_out_key: cdk_out
          npm_token: ${{ secrets.NPM_TOKEN }}
          aws_account_id: ${{ vars.AWS_ACCOUNT_ID }}
          peek_environment: prod
          command: synth --context environment=prod

  deploy:
    name: CDK Deploy
    needs: [synth]
    runs-on: ubuntu-latest
    environment: prod
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: "Configure AWS Credentials"
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ vars.AWS_ROLE_TO_ASSUME }}

      - name: Extract build target from tag
        id: extract
        env:
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: |
          TAG_NAME="${GITHUB_REF_NAME#PROD-}"
          # Extract the project name from the tag
          PROJECT_NAME=$(echo "$TAG_NAME" | sed -E 's/^[0-9]+\.[0-9]+\.[0-9]+-//')        
          # Convert project name to lowercase
          PROJECT_NAME_LOWER=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]')
          case "$PROJECT_NAME_LOWER" in
            "sideloader" | "self-guided-tour" | "virtual-tour" | "dashboard" | "casa-widget" | "insights-dashboard")
              echo "Valid Project: $PROJECT_NAME_LOWER"
              echo "PROJECT_NAME=$PROJECT_NAME_LOWER" >> $GITHUB_ENV
              ;;
            *)
              echo "Invalid Project: $PROJECT_NAME"
              echo "Valid Project filter are: SIDELOADER, SELF-GUIDED-TOUR, VIRTUAL-TOUR, DASHBOARD, CASA-WIDGET, INSIGHTS-DASHBOARD"
              exit 1
              ;;
          esac

      - name: CDK Deploy
        uses: peek-tech/tools-infra-actions/.github/actions/cdk_deploy@main
        with:
          cdk_out_key: cdk_out
          npm_token: ${{ secrets.NPM_TOKEN }}
          stack_name: ${{ env.PROJECT_NAME }}-frontend-static-website
