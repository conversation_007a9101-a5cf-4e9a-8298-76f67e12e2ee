import path from "node:path";
import CopyPlugin from "copy-webpack-plugin";
import MiniCssExtractPlugin from "mini-css-extract-plugin";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import CompressionPlugin from "compression-webpack-plugin";
import Dotenv from "dotenv-webpack";
import dotenv from "dotenv";
import HtmlWebpackPlugin from "html-webpack-plugin";
import NodePolyfillPlugin from "node-polyfill-webpack-plugin";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(new URL(import.meta.url)));

dotenv.config({ path: path.resolve(process.cwd(), "../../.env") });

export const outputConfig = {
  destPath: "./build",
};

export const sentryConfig = {
  org: "peek-technologies",
  project: "frontend",
  include: path.join(process.cwd(), outputConfig.destPath),
  authToken: process.env.PEEK_APP_SENTRY_AUTH_TOKEN,
  deleteAfterCompile: true,
};

// Entry points
// https://webpack.js.org/concepts/entry-points/
export const entryConfig = ["./src/index.tsx"];

// Copy files from src to dist
// https://webpack.js.org/plugins/copy-webpack-plugin/
export const copyPluginPatterns = {
  patterns: [{ from: "./src/assets" }],
};

export const pluginsConfig = [
  new CopyPlugin(copyPluginPatterns),
  // Load root .env file
  new Dotenv({
    path: path.resolve(process.cwd(), "../../.env"), // Path to root .env file
  }),
  // Load project-specific .env file
  new Dotenv({
    path: path.resolve(process.cwd(), ".env"), // Path to project .env file
  }),
  new NodePolyfillPlugin(),
  new MiniCssExtractPlugin(),
  new CompressionPlugin(),
];

export const rulesConfig = [
  {
    test: /\.tsx?$/,
    use: "ts-loader",
    exclude: /node_modules/,
  },
  {
    test: /\.(?:ico|gif|png|jpg|jpeg|svg)$/i,
    type: "javascript/auto",
    loader: "file-loader",
    options: {
      publicPath: "../",
      name: "[path][name].[ext]",
      context: path.resolve(__dirname, "src/assets"),
      emitFile: false,
    },
  },
  {
    test: /\.(woff(2)?|eot|ttf|otf|svg|)$/,
    type: "javascript/auto",
    exclude: /images/,
    loader: "file-loader",
    options: {
      publicPath: "../",
      context: path.resolve(__dirname, "src/assets"),
      name: "[path][name].[ext]",
      emitFile: false,
    },
  },
  {
    test: /\.css$/,
    use: [MiniCssExtractPlugin.loader, "css-loader"],
  },
];

// Production terser config options
// https://webpack.js.org/plugins/terser-webpack-plugin/#terseroptions
export const terserPluginConfig = {
  extractComments: false,
  terserOptions: {
    compress: {
      drop_console: true,
    },
  },
};

export const devServer = {
  static: {
    directory: path.join(__dirname, outputConfig.destPath),
  },
  // https: true,
  historyApiFallback: true,
  // disableHostCheck: true
};

export const resolveConfig = {
  extensions: [".tsx", ".ts", ".js"],
  plugins: [new TsconfigPathsPlugin()],
};

export const optimizationConfig = {
  minimize: true,
  usedExports: true,
};

const JSChunks = ["contentScript"];

export const getCEHtmlPlugins = (options) => {
  return JSChunks.map(
    (chunk) =>
      new HtmlWebpackPlugin({
        inject: true,
        template: "src/index.html",
        filename: `${chunk}.html`,
        chunks: [chunk],
        ...options,
      }),
  );
};

export const CEEntryConfig = {
  contentScript: "./src/content/index.tsx",
};
