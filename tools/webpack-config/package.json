{"name": "@piiqtechnologies/webpack-config", "version": "1.0.0", "description": "Webpack config for Peek", "main": "index.mjs", "type": "module", "private": true, "license": "MIT", "devDependencies": {"@sentry/webpack-plugin": "^1.19.1", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^9.1.0", "css-loader": "^6.6.0", "dotenv-webpack": "^8.0.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "https-browserify": "^1.0.0", "mini-css-extract-plugin": "^2.5.3", "node-polyfill-webpack-plugin": "^1.1.4", "terser-webpack-plugin": "^5.3.1", "ts-loader": "^9.2.6", "tsconfig-paths-webpack-plugin": "^3.5.2", "webpack": "^5.88.2", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}}