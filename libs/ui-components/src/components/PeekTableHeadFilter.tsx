import React, { useEffect, useMemo } from "react";
import { Filter2 } from "@piiqtechnologies/svg-icons";
import PeekClickAwayListener from "./PeekClickAwayListener";
import PeekPopper from "./PeekPopper";
import PeekIconButton from "./PeekIconButton";
import PeekPaper from "./PeekPaper";
import PeekList from "./PeekList";
import PeekListItem from "./PeekListItem";
import PeekListItemButton from "./PeekListItemButton";
import PeekListItemIcon from "./PeekListItemIcon";
import PeekListItemText from "./PeekListItemText";
import PeekCheckbox from "./PeekCheckbox";
import { useTheme } from "../themes";
import PeekStack from "./PeekStack";
import PeekTypography from "./PeekTypography";
import PeekTextField from "./PeekTextField";
import { fi } from "date-fns/locale";

export interface PeekTableFilter {
  id: string;
  label: string;
}

export interface PeekTableHeadFilterProps {
  filters: PeekTableFilter[];
  defaultFilters?: string[];
  onFilterChange?: (selectedFilters: string[]) => void;
  rangeFilter?: boolean; // Optional prop to indicate if the filter is a range filter
}

export const PeekTableHeadFilter = (props: PeekTableHeadFilterProps) => {
  const theme = useTheme();
  const { filters, defaultFilters, onFilterChange, rangeFilter } = props;
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const [selectedFilters, setSelectedFilters] = React.useState<string[]>([]);
  const active = !!selectedFilters?.length;

  const handleFilterSelection = (value: string) => () => {
    const currentIndex = selectedFilters.indexOf(value);
    const newSelectedFilters = [...selectedFilters];

    if (currentIndex === -1) {
      newSelectedFilters.push(value);
    } else {
      newSelectedFilters.splice(currentIndex, 1);
    }
    setSelectedFilters(newSelectedFilters);
    onFilterChange && onFilterChange(newSelectedFilters);
  };

  const toggleFilter = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const [minRannge, maxRange] = useMemo(() => {
    if (rangeFilter && filters.length > 0) {
      const validFilters = filters.map((f) => parseFloat(f.id)).filter((f) => !isNaN(f));
      const min = Math.min(...validFilters);
      const max = Math.max(...validFilters);
      return [min, max];
    }
    return [];
  }, [filters, rangeFilter]);

  useEffect(() => {
    defaultFilters && setSelectedFilters(defaultFilters);
  }, [defaultFilters]);

  return (
    <>
      <PeekIconButton
        id="filter-button"
        aria-controls={open ? "filter-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        aria-label="Show Filter Options"
        onClick={toggleFilter}
        bordered={false}
      >
        <Filter2 color={(active && theme.palette.secondary.main) || theme.palette.primary.main} />
      </PeekIconButton>
      <PeekPopper
        id="filter-menu"
        aria-labelledby="filter-button"
        anchorEl={anchorEl}
        open={open}
        role={undefined}
        placement="bottom-end"
        sx={{ zIndex: 9999 }}
      >
        {/* @ts-ignore */}
        <PeekClickAwayListener onClickAway={toggleFilter}>
          <PeekPaper elevation={3} sx={{ mt: 1 }}>
            {(props.rangeFilter && (
              <PeekStack
                direction="row"
                justifyContent="center"
                alignItems="center"
                justifyItems="center"
                spacing={1}
                sx={{ p: 1 }}
              >
                <PeekTextField
                  label="Min"
                  variant="standard"
                  size="small"
                  type="number"
                  placeholder={`${minRannge || "Min"}`}
                  value={selectedFilters[0] || ""}
                  onChange={(e) => {
                    const newFilters = [...selectedFilters];
                    newFilters[0] = e.target.value;
                    setSelectedFilters(newFilters);
                    onFilterChange && onFilterChange(newFilters);
                  }}
                  sx={{ width: 80 }}
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    disableUnderline: true,
                  }}
                  inputProps={{
                    min: minRannge || 0,
                    max: maxRange || Infinity,
                  }}
                />
                <PeekTextField
                  label="Max"
                  variant="standard"
                  size="small"
                  type="number"
                  placeholder={`${maxRange || "Max"}`}
                  value={selectedFilters[1] || ""}
                  onChange={(e) => {
                    const newFilters = [...selectedFilters];
                    newFilters[1] = e.target.value;
                    setSelectedFilters(newFilters);
                    onFilterChange && onFilterChange(newFilters);
                  }}
                  sx={{ width: 80 }}
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    disableUnderline: true,
                  }}
                  inputProps={{
                    min: minRannge || 0,
                    max: maxRange || Infinity,
                  }}
                />
              </PeekStack>
            )) || (
              <PeekList disablePadding sx={{ width: 130, maxHeight: 300, overflowY: "auto", py: 1 }}>
                {filters.map(({ id, label }) => {
                  const labelId = `checkbox-list-label-${id}`;
                  return (
                    <PeekListItem key={id} disablePadding>
                      <PeekListItemButton role={undefined} sx={{ py: 0 }} onClick={handleFilterSelection(id)} dense>
                        <PeekListItemIcon>
                          <PeekCheckbox
                            edge="start"
                            checked={selectedFilters.indexOf(id) !== -1}
                            tabIndex={-1}
                            color="success"
                            disableRipple
                            inputProps={{ "aria-labelledby": labelId }}
                            sx={{ py: 0.5 }}
                          />
                        </PeekListItemIcon>
                        <PeekListItemText id={labelId} primary={label} primaryTypographyProps={{ variant: "body1" }} />
                      </PeekListItemButton>
                    </PeekListItem>
                  );
                })}
              </PeekList>
            )}
          </PeekPaper>
        </PeekClickAwayListener>
      </PeekPopper>
    </>
  );
};

export default PeekTableHeadFilter;
