import React, { useMemo, useState } from "react";
import { Select, SelectProps } from "@mui/material";
import { styled } from "@mui/material/styles";
import { ChevronDown as DownIcon, ChevronUp as UpIcon } from "@piiqtechnologies/svg-icons";
import PeekIconButton from "./PeekIconButton";
import PeekMenuItem, { PeekMenuItemProps } from "./PeekMenuItem";

export interface PeekSelectProps extends SelectProps<any> {
  centerText?: boolean;
  showBorder?: boolean;
}

export const PeekSelectOption: React.FC<PeekMenuItemProps> = (props) => {
  return <PeekMenuItem {...props} />;
};

export const PeekSelect: React.FC<PeekSelectProps> = styled(
  ({ centerText, showBorder, MenuProps, inputProps, ...props }: PeekSelectProps) => {
    const [expanded, setExpanded] = useState(false);

    const toggleExpanded = () => {
      setExpanded(!expanded);
    };

    const isDisabled = useMemo(() => {
      return props.disabled || inputProps?.disabled ? props.disabled || inputProps?.disabled : expanded;
    }, []);

    return (
      <Select
        onOpen={toggleExpanded}
        onClose={toggleExpanded}
        open={isDisabled ? false : expanded}
        MenuProps={{
          PaperProps: {
            sx: {
              mt: 1.2,
              borderRadius: 0.8,
            },
          },
          MenuListProps: {
            sx: {
              pt: 0,
              pb: 0,
            },
          },
          ...MenuProps,
        }}
        IconComponent={() => (
          <PeekIconButton
            color="primary"
            bordered={false}
            onClick={toggleExpanded}
            sx={{
              px: 0,
              mr: 1.5,
              opacity: 0.5,
              ":hover": { bgcolor: "transparent" },
            }}
          >
            {(expanded && !isDisabled && <UpIcon height={18} width={18} />) || <DownIcon height={18} width={18} />}
          </PeekIconButton>
        )}
        inputProps={inputProps}
        {...props}
      />
    );
  },
)(({ centerText, showBorder, theme }) => ({
  ...theme.typography.h5,
  borderRadius: theme.spacing(0.8),
  backgroundColor: theme.palette.primary.contrastText,
  border: `${theme.spacing(0.05)} solid ${theme.palette.grey[100]}`,
  ...(centerText && { textAlign: "center" }),
  ...(showBorder && {
    border: `${theme.spacing(0.05)} solid ${theme.palette.grey[100]}`,
    boxShadow: "none",
  }),

  "& .MuiSelect-select": {
    ...theme.typography.h5,
    padding: `${theme.spacing(0.6)} ${theme.spacing(1.5)} !important`,
    border: "none",
    minHeight: "unset !important",

    "&.Mui-disabled": {
      backgroundColor: theme.palette.grey[50],
    },
  },

  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
    borderStyle: "unset !important",
  },
}));

export default PeekSelect;
