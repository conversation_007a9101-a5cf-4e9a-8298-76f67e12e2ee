import * as React from "react";
import { SVGProps, memo } from "react";
const SvgPrpopertyMapPinFilled = ({
  text,
  textColor,
  ...props
}: SVGProps<SVGSVGElement> & { text: string; textColor: string }) => (
  <svg width="1em" height="1em" viewBox="0 0 50 57" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g filter="url(#prpoperty-map-pin-filled_svg__a)">
      <path
        d="M36.25 23.313c0 10.125-11.25 18-11.25 18s-11.25-7.876-11.25-18a11.25 11.25 0 0 1 22.5 0Z"
        fill="currentColor"
      />
      <path
        d="M36.25 23.313c0 10.125-11.25 18-11.25 18s-11.25-7.876-11.25-18a11.25 11.25 0 0 1 22.5 0Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <text x="22" y="30" fill={textColor}>
      {text}
    </text>
    <defs>
      <filter
        id="prpoperty-map-pin-filled_svg__a"
        x={0.25}
        y={0.563}
        width={49.5}
        height={56.25}
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
        <feMorphology radius={4} operator="dilate" in="SourceAlpha" result="effect1_dropShadow_176_7163" />
        <feOffset dy={2} />
        <feGaussianBlur stdDeviation={4.5} />
        <feColorMatrix values="0 0 0 0 0.679167 0 0 0 0 0.679167 0 0 0 0 0.679167 0 0 0 0.13 0" />
        <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_176_7163" />
        <feBlend in="SourceGraphic" in2="effect1_dropShadow_176_7163" result="shape" />
      </filter>
    </defs>
  </svg>
);
const Memo = memo(SvgPrpopertyMapPinFilled);
export default Memo;
