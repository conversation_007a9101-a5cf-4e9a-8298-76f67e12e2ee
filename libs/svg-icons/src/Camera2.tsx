import * as React from "react";
import { SVGProps, memo } from "react";
const SvgCamera2 = (props: SVGProps<SVGSVGElement>) => (
  <svg width="1em" height="1em" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M20.406 20h-15a1.5 1.5 0 0 1-1.5-1.5V8a1.5 1.5 0 0 1 1.5-1.5h3l1.5-2.25h6l1.5 2.25h3a1.5 1.5 0 0 1 1.5 1.5v10.5a1.5 1.5 0 0 1-1.5 1.5Z"
      stroke="#8E8E9C"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.906 16.25a3.375 3.375 0 1 0 0-6.75 3.375 3.375 0 0 0 0 6.75Z"
      stroke="#8E8E9C"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const Memo = memo(SvgCamera2);
export default Memo;
