export interface PropertyLocation {
  lat: number;
  lng: number;
}

export interface InsightsProperty {
  id: string;
  label: string;
  point: PropertyLocation;
  name: string;
  photo: string;
  address: string;
  yearBuilt: string;
  assetClass: string;
  unitCount: number;
  amenitiesCount: number;
  propertyType: "peek" | "non-peek";
  hasTour: boolean;
  hasPrice: boolean;
  selected?: number;
  owner?: string;
  manager?: string;
  virtualTourUrl?: string;
}

export interface InsightsFilters {
  ids?: string[];
  location?: PropertyLocation;
  radius?: string;
  buildingType?: string[];
  numberOfUnits?: string;
  layout?: string[];
  sf?: InsightsSquareFoot;
  amenities?: string[];
  yearBuilt?: InsightsYearBuilt[];
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
}

export interface InsightsSquareFoot {
  min: number;
  max: number;
}

export interface InsightsAmenities {
  name: string;
}

export interface InsightsBuildingTypes {
  name: string;
}

export interface InsightsYearBuilt {
  min: number;
  max: number;
}

export interface InsightsPropertyMetrics {
  avgAskingRent: number;
  medianAskingRent: number;
  avgAskingRentPSF: number;
  medianAskingRentPSF: number;
  concessionsPercent: number;
  avgNER: number;
  medianNER: number;
  avgNERPSF: number;
  medianNERPSF: number;
  demandIndex: number;
  virtualTours: number;
  selfGuidedTours: number;
}

export interface InsightsPropertyMetricsByDate {
  [date: string]: InsightsPropertyMetrics;
}
