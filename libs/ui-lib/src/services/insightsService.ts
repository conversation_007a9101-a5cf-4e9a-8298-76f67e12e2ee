import { AxiosRequestConfig, AxiosResponse } from "axios";
import baseV3Service from "./base/baseV3Service";
import {
  InsightsPropertyLease,
  InsightsProperty,
  InsightsUnit,
  InsightsAmenities,
  InsightsAmenitiesAndFeatures,
  InsightsPropertyMetricsByDate,
} from "@types";

export interface InsightsPropertiesResponse {
  data: InsightsProperty[];
  totalCount: number;
}

export interface UnitsInsightsOverview {
  title: string;
  availble: string;
  leased: string;
}

export interface InsightsPropertiesAveragesResponse {
  metro: InsightsPropertyMetricsByDate;
  properties?: {
    [propertyId: string]: {
      name: string;
      averages: InsightsPropertyMetricsByDate;
    };
  };
}

export interface UnitsLevelInsightsResponse {
  data: InsightsUnit[];
  totalCount: number;
}

const getInsightsProperties = (config?: AxiosRequestConfig): Promise<AxiosResponse<InsightsPropertiesResponse>> => {
  return baseV3Service.get("insights/properties", config);
};

const getInsightsPropertyLeases = (config?: AxiosRequestConfig): Promise<AxiosResponse<InsightsPropertyLease[]>> => {
  return baseV3Service.get("insights/leases-overview", config);
};

const getUnitsInsightsOverview = (config?: AxiosRequestConfig): Promise<AxiosResponse<UnitsInsightsOverview[]>> => {
  return baseV3Service.get("insights/units-overview", config);
};

const getUnitsLevelInsights = (config?: AxiosRequestConfig): Promise<AxiosResponse<UnitsLevelInsightsResponse[]>> => {
  return baseV3Service.get("insights/units-level", config);
};

const getInsightsAmenities = (config?: AxiosRequestConfig): Promise<AxiosResponse<InsightsAmenities[]>> => {
  return baseV3Service.get("insights/amenities", config);
};

const getInsightsUnitsAmenities = (config?: AxiosRequestConfig): Promise<AxiosResponse<InsightsAmenities[]>> => {
  return baseV3Service.get("insights/units-amenities", config);
};

const getInsightsBuildingTypes = (config?: AxiosRequestConfig): Promise<AxiosResponse<string[]>> => {
  return baseV3Service.get("insights/building-types", config);
};

const getInsightsYearsBuilt = (config?: AxiosRequestConfig): Promise<AxiosResponse<string[]>> => {
  return baseV3Service.get("insights/years-built", config);
};

const getInsightsPropertyAverages = (
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<InsightsPropertiesAveragesResponse>> => {
  return baseV3Service.get("insights/property-averages", config);
};

const getInsightsAmenitiesAndFeatures = (
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<InsightsAmenitiesAndFeatures[]>> => {
  return baseV3Service.get(`insights/amenities-and-features`, config);
};

const getInsightsUnitsAndFeatures = (
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<InsightsAmenitiesAndFeatures[]>> => {
  return baseV3Service.get("insights/units-and-features", config);
};

export {
  getInsightsProperties,
  getInsightsPropertyLeases,
  getUnitsInsightsOverview,
  getUnitsLevelInsights,
  getInsightsAmenities,
  getInsightsUnitsAmenities,
  getInsightsBuildingTypes,
  getInsightsYearsBuilt,
  getInsightsPropertyAverages,
  getInsightsAmenitiesAndFeatures,
  getInsightsUnitsAndFeatures,
};
