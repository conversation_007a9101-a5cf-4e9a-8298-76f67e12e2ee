import { AxiosRequestConfig, AxiosResponse } from "axios";
import baseV3Service from "./base/baseV3Service";

export interface AdminLoginPayload {
  name: string;
  email: string;
  id_token: string;
}

export interface LoginPayload {
  email: string;
  password: string;
  origin: string;
}

export interface ForgotPasswordPayload {
  email: string;
}

export interface ResetPasswordPayload {
  token: string;
  password: string;
}

export interface LoginResponse {
  token: string;
}
export interface SSOLoginPayload {
  temp_token: string;
}

export interface GMBAuth {
  accessToken?: string;
  authUrl?: string;
}
export interface GMBAuthResponse {
  authResult: GMBAuth;
}
export interface GoogleMyBusinessCredsPayload {
  code: string;
}

const login = (payload: LoginPayload, config?: AxiosRequestConfig): Promise<AxiosResponse<LoginResponse>> => {
  return baseV3Service.post("auth/login", payload, config);
};

const forgotPassword = (
  payload: ForgotPasswordPayload,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<Record<string, never>>> => {
  return baseV3Service.post("auth/forgot-password", payload, config);
};

const resetPassword = (
  payload: ResetPasswordPayload,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<LoginResponse>> => {
  return baseV3Service.post("auth/reset-password", payload, config);
};

const getGoogleOAuthURL = (config?: AxiosRequestConfig): Promise<AxiosResponse<string>> => {
  return baseV3Service.get("auth/google-oauth-url", config);
};

const updateGoogleMyBusinessCreds = (
  payload?: GoogleMyBusinessCredsPayload,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<Record<string, never>>> => {
  return baseV3Service.put("auth/google-credentials", payload, config);
};

const proxyLogin = (userId: string, config: AxiosRequestConfig = {}): Promise<AxiosResponse<{ token: string }>> => {
  return baseV3Service.post(`auth/proxy-login`, { userId }, config);
};

const endProxySession = (config: AxiosRequestConfig = {}): Promise<AxiosResponse<{ token: string }>> => {
  return baseV3Service.post(`auth/end-proxy-session`, {}, config);
};

const deleteGmbAccount = (config?: AxiosRequestConfig): Promise<Record<string, never>> => {
  return baseV3Service.delete("auth/google-credentials", config);
};

export {
  login,
  forgotPassword,
  resetPassword,
  updateGoogleMyBusinessCreds,
  proxyLogin,
  endProxySession,
  getGoogleOAuthURL,
  deleteGmbAccount,
};
