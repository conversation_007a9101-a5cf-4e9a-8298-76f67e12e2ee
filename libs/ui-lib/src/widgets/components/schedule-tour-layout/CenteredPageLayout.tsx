import { PeekContainer, PeekStack, useTheme } from "@piiqtechnologies/ui-components";
import React from "react";

interface CenteredPageLayoutProps {
  Logo?: React.FC;
}

const CenteredPageLayout: React.FC<CenteredPageLayoutProps> = ({ children, Logo }) => {
  const theme = useTheme();
  const height = window.innerHeight;

  return (
    <PeekContainer
      maxWidth="sm"
      sx={{
        display: "flex",
        minHeight: height,
        alignItems: "baseline",
        pt: 3,
        pb: 10.2,
        overflowY: "auto",
        backgroundColor: theme.palette.grey[50],
      }}
      data-testid="centered-page-layout"
    >
      {/* PEEK SGT content */}
      <PeekStack direction="column" width="100%" spacing={3}>
        {Logo && <Logo />}
        {children}
      </PeekStack>
    </PeekContainer>
  );
};

export default CenteredPageLayout;
