import React from "react";
import { PeekContainedIconButton, PeekTooltip, PeekTypography } from "@piiqtechnologies/ui-components";
import { HotspotType, Infospot } from "@types";
import Linkify from "linkify-react";
import HotspotIcon from "./HotspotIcon";
import { RelatedSpaceContext } from "@pannellum-viewer/pannellumUtils";

export interface InfoHotspotProps extends Infospot {
  relatedContext: RelatedSpaceContext;
  helpers: Record<string, any>;
  sceneId: string;
}

export const InfoHotspot: React.FC<InfoHotspotProps> = ({ _id, text, helpers, sceneId, relatedContext }) => {
  const onTooltipOpen = () => {
    helpers.clickEventAnalytics("Infospot", {
      roomId: sceneId,
      roomName: relatedContext?.node?.label,
      hotspotId: _id,
      hotspotName: text,
    });
  };
  return (
    <PeekTooltip
      PopperProps={{
        disablePortal: true,
      }}
      sx={{
        width: "200px",
        "& .MuiTooltip-tooltip": {
          width: "100%",
          boxShadow: "none",
          whiteSpace: "normal",
        },
      }}
      enterTouchDelay={0}
      leaveTouchDelay={3000}
      onOpen={onTooltipOpen}
      placement="top"
      title={
        <Linkify
          options={{
            attributes: {
              onClick: (event: any) => {
                window.open(event.target.href, "_blank");
              },
            },
          }}
        >
          {text}
        </Linkify>
      }
    >
      <PeekContainedIconButton
        id="info-button"
        sx={{ backgroundColor: "secondary.main", ":hover": { backgroundColor: "secondary.main" } }}
        aria-label="info hotspot"
      >
        <PeekTypography color="text.secondary" fontSize={22}>
          <HotspotIcon type={HotspotType.Info} />
        </PeekTypography>
      </PeekContainedIconButton>
    </PeekTooltip>
  );
};

export default InfoHotspot;
