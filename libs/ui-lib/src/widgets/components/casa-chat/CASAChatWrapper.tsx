import React, { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getItem, setItem } from "@analytics/storage-utils";
import { CASAChatActor, CASAChatType, PromptType } from "./const";
import { useCreateProspect, useInitCASAChat, useSendCASAMessage } from "@apis";
import CASAChat, { Message } from "./CASAChat";
import { useAnalytics, useContact } from "@utils";
import { jwtDecode } from "jwt-decode";
import { ProspectOrigins } from "@types";

export type CASAErrorPrompt = {
  type: PromptType;
  message: string;
  linkId?: string;
};

type CASAChatProps = {
  type: CASAChatType;
  prospectId?: string;
  communityId: string;
  anonymousId: string;
  sessionId: string;
  spaceId?: string;
  prompts: Generator<string[] | (PromptType | (string | false)[])[], void, unknown>;
  sxProps?: Record<string, any>;
  errorPrompt?: CASAErrorPrompt[];
  processChatLink?: (event: React.MouseEvent) => void;
  byPassProspectCreation?: boolean;
  prospectCreationOnlyOnHandoff?: boolean;
  placeholder?: string;
};

const CASA_CHAT_TOKEN_KEY = "casa_chat_token";
const CASA_CHAT_MESSAGES_KEY = "casa_chat_messages";
const CASA_EMAIL_VALIDATION_COUNT = "casa_email_validation_count";

const getCASAChatDetails = (key: string) => {
  let casaChat = null;
  try {
    casaChat = getItem(key, { storage: "sessionStorage" });
  } catch {
    console.error(`Peek Error: Storage error. Unable to READ ${key} details!!!`);
  }
  return casaChat;
};

const saveCASAChatDetails = (key: string, value: any) => {
  try {
    setItem(key, value, { storage: "sessionStorage" });
  } catch {
    console.error(`Peek Error: Storage error. Unable to SAVE ${key} details!!!`);
  }
};

export function CASAChatWrapper({
  prospectId,
  communityId,
  anonymousId,
  sessionId,
  spaceId,
  prompts,
  sxProps,
  errorPrompt,
  processChatLink,
  type,
  byPassProspectCreation,
  prospectCreationOnlyOnHandoff,
  placeholder,
}: CASAChatProps) {
  const initToken = getCASAChatDetails(CASA_CHAT_TOKEN_KEY);
  const casaMessages = getCASAChatDetails(CASA_CHAT_MESSAGES_KEY)?.map(
    ([_type, _message, _complete, link]: Message) => [_type, _message, true, link],
  );
  const [messages, setMessages] = useState<Message[]>(casaMessages || []);
  const [initCASAChat, casaToken, casaTokenInProgress, casasTokenError] = useInitCASAChat();
  const [sendCASAMessage, casaResponse, casaResponseInProgress, casaResponseError, messageInProgress] =
    useSendCASAMessage();
  const [_createProspect, prospect, createProspectInProgress, createProspectError] = useCreateProspect();
  const { setContactDetails } = useContact();
  const analytics = useAnalytics();
  const [previousSpaceId, setPreviousSpaceId] = useState<string | null>(null);
  const prospectEmailValidationCount = useRef<number>(getCASAChatDetails(CASA_EMAIL_VALIDATION_COUNT) || 0);
  const [prospectCreationNotRequired, setProspectCreationNotRequired] =
    useState<boolean>(!!prospectCreationOnlyOnHandoff);
  const [prospectCreationRequiredForHandoff, setProspectCreationRequiredForHandoff] = useState<boolean>(false);

  const updateProspectEmailValidationCount = () => {
    prospectEmailValidationCount.current += 1;
    byPassProspectCreation && prospectEmailValidationCount.current === 2 && setProspectCreationNotRequired(true);
    saveCASAChatDetails(CASA_EMAIL_VALIDATION_COUNT, prospectEmailValidationCount.current);
  };

  const resetProspectCreationForHandoff = () => {
    setProspectCreationRequiredForHandoff(false);
  };

  const chatToken = useMemo(() => casaToken || initToken, [initToken, casaToken]);

  const saveMessages = (message: Message[], replaceLast?: boolean) => {
    const updatedMessages = [...((replaceLast && messages.slice(0, messages.length - 1)) || messages), ...message];
    saveCASAChatDetails(CASA_CHAT_MESSAGES_KEY, updatedMessages);
    setMessages(updatedMessages);
  };

  const errorMessages = useMemo(() => {
    return (
      (errorPrompt &&
        errorPrompt.map((prompt: CASAErrorPrompt) => {
          return [
            (prompt.type === PromptType.TEXT && CASAChatActor.error) || CASAChatActor.suggestion,
            prompt.message,
            true,
            prompt.linkId,
          ];
        })) || [[CASAChatActor.error, "We are facing issue. Please try again later"]]
    );
  }, [errorPrompt]);

  useEffect(() => {
    if (casasTokenError || casaResponseError) {
      handleErrors();
      prospectCreationRequiredForHandoff && resetProspectCreationForHandoff();
    }
  }, [casasTokenError, casaResponseError, prospectCreationRequiredForHandoff, createProspectError]);

  useEffect(() => {
    if (casaToken) {
      saveCASAChatDetails(CASA_CHAT_TOKEN_KEY, casaToken);
    }
  }, [casaToken]);

  useEffect(() => {
    if (casaResponse) {
      // Extracting the link text and URL using a single regex
      const linkRegex = /\[(.*?)\]\((.*?)\)/;
      const linkMatch = casaResponse.match(linkRegex);
      let updatedContent = casaResponse;
      if (linkMatch) {
        const linkText = linkMatch[1];
        const linkURL = linkMatch[2];
        updatedContent = casaResponse.replace(
          linkRegex,
          `<a href="${linkURL}" target="_blank" rel="noopener">${linkText}</a>`,
        );
      }
      if (updatedContent.includes("<json>")) {
        const jsonRegex = /<json>([\s\S]*?)<\/json>/;
        const jsonMatch = updatedContent.match(jsonRegex);
        const suggestions: Message[] = [];
        if (jsonMatch) {
          const jsonString = jsonMatch[1].trim();
          const responseData = JSON.parse(jsonString);
          responseData?.data?.forEach((data: any) => {
            const { name, link, handoff } = data;
            if (handoff) {
              setProspectCreationRequiredForHandoff(true);
            }
            suggestions.push([CASAChatActor.suggestion, name, true, link]);
          });
          updatedContent = updatedContent.replace(jsonRegex, "").trim().replace(/\n\n/g, "<br/>");
          if (!messageInProgress) {
            saveMessages([[CASAChatActor.casa, updatedContent, !messageInProgress], ...suggestions], true);
          }
        }
      } else {
        saveMessages([[CASAChatActor.casa, updatedContent, !messageInProgress]], true);
      }
      if (!messageInProgress) {
        analytics?.track("casa_chat_message_received");
      }
    }
  }, [casaResponse, messageInProgress]);

  useEffect(() => {
    if (
      (prospectCreationNotRequired || prospectId) &&
      !casaTokenInProgress &&
      ((spaceId && previousSpaceId && spaceId !== previousSpaceId) || !chatToken)
    ) {
      initCASAChat({
        prospectId,
        communityId,
        anonymousId,
        spaceId,
        type,
        sessionId,
      });
    }
  }, [
    communityId,
    prospectId,
    anonymousId,
    type,
    spaceId,
    previousSpaceId,
    sessionId,
    chatToken,
    prospectCreationNotRequired,
  ]);

  useEffect(() => {
    if (chatToken) {
      const decodedToken = jwtDecode(chatToken) as { spaceId: string };
      if (decodedToken?.spaceId !== spaceId) {
        setPreviousSpaceId(decodedToken.spaceId);
      }
    }
  }, [spaceId, chatToken]);

  const handleErrors = () => {
    //@ts-ignore
    saveMessages(errorMessages);
    analytics?.track("casa_chat_error");
  };

  const sendMessage = async (message: string, handoffProspectId?: string) => {
    let token = chatToken;
    if (!token || (prospectCreationRequiredForHandoff && handoffProspectId)) {
      token = await initCASAChat({
        prospectId: handoffProspectId || prospectId,
        communityId,
        anonymousId,
        spaceId,
        type,
        sessionId,
      });
    }
    if (token) {
      analytics?.track("casa_chat_message_sent", {
        message,
      });
      sendCASAMessage({
        message,
        token,
      });
      if (prospectCreationRequiredForHandoff) {
        setTimeout(resetProspectCreationForHandoff, 0);
      }
    }
  };

  const getPrompts = useCallback(() => {
    let prompt = prompts.next().value;
    const promptMessages = [] as [CASAChatActor, ReactNode][];
    while (!!prompt) {
      const [type, message] = prompt;
      promptMessages.push([(type === PromptType.TEXT && CASAChatActor.casa) || CASAChatActor.suggestion, message]);
      if (!prospectId && !prospectCreationNotRequired) break;
      prompt = prompts.next().value;
    }
    !!promptMessages.length && saveMessages(promptMessages);
  }, [prospectId, prompts, prospectCreationNotRequired]);

  const createProspect = async (payload: { email: string; firstName?: string; lastName?: string }) => {
    //@ts-ignore
    setContactDetails(payload);
    const prospectData = await _createProspect({
      ...payload,
      communityId,
      origin: ProspectOrigins.CASAChat,
    });
    return prospectData;
  };

  useEffect(() => {
    if (
      casaToken &&
      !previousSpaceId &&
      !prospectCreationRequiredForHandoff &&
      (prospectCreationNotRequired || prospectId)
    ) {
      getPrompts();
    }
  }, [casaToken, prospectId, previousSpaceId, prospectCreationNotRequired]);

  useEffect(() => {
    if (!prospectId && !casaMessages?.length) {
      getPrompts();
    }
  }, [prospectId, casaMessages]);

  useEffect(() => {
    if (prospect) {
      setContactDetails({ ...prospect, _id: prospect.prospectId, communityId });
      !prospectCreationRequiredForHandoff &&
        setMessages([[CASAChatActor.casa, "Great! Thanks for providing your email. How can I assist you today?"]]);
    }
  }, [prospect]);

  return (
    <CASAChat
      messages={messages}
      setMessages={saveMessages}
      sendMessage={sendMessage}
      sxProps={sxProps}
      prospectExists={Boolean(prospectId || chatToken)}
      responseLoading={casaResponseInProgress || casaTokenInProgress || createProspectInProgress}
      messageInProgress={messageInProgress}
      processChatLink={processChatLink}
      byPassProspectCreation={Boolean(byPassProspectCreation && prospectEmailValidationCount.current === 1)}
      onEmailValidationError={updateProspectEmailValidationCount}
      prospectCreationRequiredForHandOff={prospectCreationRequiredForHandoff}
      createProspect={createProspect}
      placeholder={placeholder}
    />
  );
}

export default React.memo(CASAChatWrapper);
