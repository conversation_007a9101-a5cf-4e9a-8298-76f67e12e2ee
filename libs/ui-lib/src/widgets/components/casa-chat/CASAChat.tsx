import { <PERSON>eek<PERSON><PERSON>, PeekPaper, PeekStack } from "@piiqtechnologies/ui-components";
import React, { ReactNode, useEffect, useMemo, useState } from "react";
import PromptText from "./PromptText";
import Message from "./Message";
import Suggestions from "./Suggestions";
import { CASAChatActor } from "./const";
import Loader from "./Loader";
import { ProspectForm } from "./prospect-form";

const emailRegex =
  // eslint-disable-next-line
  /([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?))/gi;

export type Message = [CASAChatActor, ReactNode, boolean?, string?];

type CASAChatProps = {
  prospectExists: boolean;
  sxProps?: Record<string, any>;
  messages: Message[];
  setMessages: (messages: Message[]) => void;
  sendMessage: (message: string, prospectId?: string) => void;
  responseLoading: boolean;
  messageInProgress: boolean;
  processChatLink?: (event: React.MouseEvent) => void;
  byPassProspectCreation: boolean;
  onEmailValidationError: () => void;
  prospectCreationRequiredForHandOff: boolean;
  createProspect: ({ email }: { email: string }) => void;
  placeholder?: string;
};

export function CASAChat({
  prospectExists,
  sendMessage,
  sxProps,
  messages,
  setMessages,
  responseLoading,
  messageInProgress,
  processChatLink,
  byPassProspectCreation,
  onEmailValidationError,
  prospectCreationRequiredForHandOff,
  createProspect,
  placeholder,
}: CASAChatProps) {
  const [promptText, setPromptText] = useState<string>(null!);
  const messagesRef = React.useRef<HTMLDivElement>(null!);

  const validateEmail = (emailPrompt: string) => {
    // Extracted email from user input
    const extractedEmail = emailPrompt.match(emailRegex);
    if (!extractedEmail) {
      onEmailValidationError();
      setMessages([
        [CASAChatActor.user, emailPrompt],
        [
          CASAChatActor.casa,
          (!byPassProspectCreation &&
            "Thank you for your message! It seems like the email address you provided is not in a valid format, or you didn't include an email. To better assist you, please type your valid email address like this: <i><EMAIL></i>.") ||
            "No problem! We can continue without an email address. How can I assist you further?",
        ],
      ]);
      return;
    }
    const email = extractedEmail[0];
    setMessages([[CASAChatActor.user, emailPrompt]]);
    createProspect({ email });
  };

  const validateEmailForHandoff = async (emailPrompt: string) => {
    // Extracted email from user input
    const extractedEmail = emailPrompt.match(emailRegex);
    let handoffProspect;
    if (extractedEmail) {
      const email = extractedEmail[0];
      handoffProspect = await createProspect({
        email,
      });
    }
    setMessages([
      [CASAChatActor.user, promptText],
      [CASAChatActor.casa, ""],
    ]);
    sendMessage(promptText, handoffProspect?.prospectId);
  };

  useEffect(() => {
    if (!promptText) return;
    if (!prospectExists) {
      validateEmail(promptText);
      return;
    }
    if (prospectCreationRequiredForHandOff) {
      validateEmailForHandoff(promptText);
      return;
    }
    setMessages([
      [CASAChatActor.user, promptText],
      [CASAChatActor.casa, ""],
    ]);
    sendMessage(promptText);
  }, [promptText, prospectCreationRequiredForHandOff]);

  useEffect(() => {
    const messagesElm = messagesRef.current;
    const lastMessage = messagesElm.lastElementChild as HTMLDivElement;
    if (lastMessage) {
      const offset = lastMessage.offsetTop + lastMessage.clientHeight;
      // Scroll to the last message's offset
      messagesElm.scrollTo({
        top: offset,
        behavior: "smooth",
      });
    }
  }, [messages]);

  const isLoader = useMemo(() => {
    return responseLoading;
  }, [responseLoading]);

  const onPromptChange = (prompt: string) => {
    setPromptText(prompt);
    setTimeout(() => {
      setPromptText("");
    }, 0);
  };

  return (
    <PeekPaper sx={{ pl: 3, py: 2, ...sxProps }} elevation={0}>
      <PeekStack spacing={2}>
        <PeekStack
          ref={messagesRef}
          spacing={1}
          sx={{
            overflowY: "auto",
            paddingRight: 2.8,
            height: {
              md: "calc(60vh - 164px)",
            },
            maxHeight: {
              xs: "calc(70vh - 174px)",
            },
            pb: {
              xs: 1,
              md: 0,
            },
          }}
        >
          {messages.map(([actor, message, completed = true, messageId], index) => {
            return (
              !!message &&
              ((actor !== CASAChatActor.suggestion && (
                <Message
                  key={index}
                  type={actor}
                  message={message}
                  completed={completed}
                  processChatLink={processChatLink}
                />
              )) || (
                <Suggestions
                  key={index}
                  message={message as string[]}
                  disabled={responseLoading || messageInProgress}
                  onSuggestionClick={onPromptChange}
                  // @ts-ignore
                  processChatLink={processChatLink}
                  suggestionId={messageId as string}
                />
              ))
            );
          })}
          {isLoader && <Message type={CASAChatActor.casa} message={<Loader />} completed />}
          {!isLoader && prospectCreationRequiredForHandOff && (
            <ProspectForm createProspect={createProspect} sendMessage={sendMessage} setMessages={setMessages} />
          )}
        </PeekStack>
        <PeekBox pr={3}>
          <PromptText onChange={onPromptChange} responseLoading={responseLoading || messageInProgress} placeholder={placeholder} />
        </PeekBox>
      </PeekStack>
    </PeekPaper>
  );
}

export default CASAChat;
