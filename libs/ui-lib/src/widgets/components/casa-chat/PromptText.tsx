import React, { useState } from "react";
import { PeekIconButton, PeekInputAdornment, PeekTextField, useTheme } from "@piiqtechnologies/ui-components";
import { PaperPlaneTilt } from "@piiqtechnologies/svg-icons";

type PromptTextProps = {
  onChange: (messageText: string) => void;
  placeholder?: string;
  responseLoading: boolean;
  startAdornment?: React.ReactNode;
};

function PromptText({ onChange, placeholder, responseLoading, startAdornment }: PromptTextProps) {
  const theme = useTheme();
  const [prompt, setPrompt] = useState<string>("");

  const handleClick = () => {
    if (!responseLoading) {
      onChange(prompt);
      setPrompt("");
    }
  };

  return (
    <PeekTextField
      sx={{
        m: 0,
        minWidth: "100%",
        backgroundColor: "common.white",
        "& .MuiInput-root": {
          mt: 0,
          borderRadius: 7.5,
          borderWidth: "0.5px",
          borderStyle: "solid",
          borderColor: "transparent",
          backgroundImage: `
          linear-gradient(0deg,${theme.palette.common.white}, ${theme.palette.common.white}), 
          linear-gradient(142.32deg, ${theme.palette.secondary.main} -8.3%, ${theme.palette.primary.main} 130.23%)`,
          backgroundOrigin: "border-box",
          backgroundClip: "content-box, border-box",
          "&.Mui-focused": {
            borderColor: "transparent",
          },
        },
      }}
      variant="standard"
      value={prompt || ""}
      fullWidth
      onKeyDown={(ev) => {
        if (ev.key === "Enter" && prompt && !responseLoading) {
          handleClick();
          ev.preventDefault();
        }
      }}
      onChange={(e) => setPrompt(e.target.value)}
      InputLabelProps={{ required: true }}
      InputProps={{
        placeholder: placeholder || "Enter your message",
        disableUnderline: true,
        "aria-label": "Enter your message",
        startAdornment: startAdornment,
        endAdornment: (
          <PeekInputAdornment onClick={handleClick} position="start">
            <PeekIconButton disabled={!prompt || responseLoading} bordered={false} color="primary">
              <PaperPlaneTilt height={20} width={20} />
            </PeekIconButton>
          </PeekInputAdornment>
        ),
      }}
    />
  );
}

export default PromptText;
