import { PeekB<PERSON>, PeekDivider, PeekStack } from "@piiqtechnologies/ui-components";
import { useAnalytics } from "@utils";
import React, { useEffect, useRef, useState } from "react";

const drawerBleeding = 72;

interface ClickableEdgeDrawerProps {
  children: React.ReactNode;
  DrawerBleedingContent: React.FC;
  eventIdentification?: string;
  customBleedingArea?: number;
  onToggle?: (newOpen: boolean) => void;
}

export default function ClickableEdgeDrawer({
  children,
  DrawerBleedingContent,
  eventIdentification,
  customBleedingArea,
  onToggle,
}: ClickableEdgeDrawerProps) {

  const bleedingArea = customBleedingArea || drawerBleeding;
  const analytics = useAnalytics();
  const [open, setOpen] = useState(false);
  const [refHeight, setRefHeight] = useState(0);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      setRefHeight(ref.current.offsetHeight);
    }
  }, [open, children]);

  const handleToggle = (newOpen: boolean) => () => {
    setOpen(newOpen);
    onToggle?.(newOpen);
    if (newOpen) {
      analytics?.track(`${eventIdentification}_opened`);
    }
  };

  const handleClickAway = (event: React.MouseEvent) => {
    if (open) {
      handleToggle(false)();
      event.stopPropagation();
    }
  };

  return (
    <>
      {/* BACKGROUND */}
      <PeekBox
        onClick={handleClickAway}
        sx={{
          height: "100%",
          width: "100%",
          opacity: open ? 0.5 : 0,
          backgroundColor: "black",
          position: "absolute",
          top: 0,
          left: 0,
          mt: "0px !important",
          transition: "opacity 0.3s ease-in",
          pointerEvents: open ? "auto" : "none",
        }}
      />
      {/* DRAWER */}
      <PeekStack
        direction={"column"}
        sx={{
          position: "absolute",
          maxHeight: "80vh",
          right: 0,
          left: 0,
          bottom: 0,
          boxShadow: "0px -2px 4px #ADADAD21",
          backgroundColor: "common.white",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          transform: open ? "translateY(0)" : `translateY(calc(100% - ${bleedingArea - 16}px))`,
          transition: "transform 0.3s ease-in",
          userSelect: "none",
        }}
      >
        <PeekBox onClick={handleToggle(!open)} sx={{ userSelect: "none" }}>
          <DrawerBleedingContent />
        </PeekBox>
        <PeekDivider
          light
          orientation="horizontal"
          flexItem
          sx={{
            height: "1px",
            opacity: open ? 1 : 0,
            transition: "opacity 0.3s ease-in",
          }}
        />
        <PeekBox
          ref={ref}
          sx={{
            p: 2,
            userSelect: "none",
            maxHeight: open ? "70vh" : "0px",
            overflow: "hidden",
            backgroundColor: "common.white",
            transition: "max-height 0.3s ease-in",
            paddingTop: open ? 2 : 0,
            paddingBottom: open ? 2 : 0,
          }}
        >
          {children}
        </PeekBox>
      </PeekStack>
    </>
  );
}
