import { ArrowSquareOut, BookOpen, MapTrifold } from "@piiqtechnologies/svg-icons";
import { PeekButton, PeekStack, useTheme } from "@piiqtechnologies/ui-components";
import { CommunityTours } from "@types";
import { getTourLabel, useAnalytics } from "@utils";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { TabLeavingWarning } from "@widgets/components/tab-leaving-warning";
import React, { useState } from "react";

interface CTAButtonProps {
  label: string;
  onClick: () => void;
  startIcon?: React.ReactNode;
}

const CTAButton = ({ label, onClick, startIcon }: CTAButtonProps) => {
  const theme = useTheme();
  return (
    <PeekButton
      onClick={onClick}
      startIcon={startIcon}
      sx={{
        borderRadius: theme.spacing(2),
        height: `${theme.spacing(3.2)} !important`,
        minWidth: "fit-content",
        width: "fit-content",
        boxShadow: "0px 2px 4px #ADADAD21",
        textTransform: "none",
        border: `1px solid ${theme.palette.grey[100]}`,
        backgroundColor: theme.palette.common.white,
        px: theme.spacing(1.2),
      }}
    >
      {label}
    </PeekButton>
  );
};

enum WARNING_TYPE {
  COMMUNITY_MAP = "COMMUNITY_MAP",
  COMMUNITY_GUIDE = "COMMUNITY_GUIDE",
}

export const CommunityCTA = () => {
  const theme = useTheme();
  const { community } = useScheduleTour();
  const analytics = useAnalytics();
  const [warning, setWarning] = useState<WARNING_TYPE | null>(null);

  const onWarningConfirm = () => {
    let communityURL = "";
    switch (warning) {
      case WARNING_TYPE.COMMUNITY_MAP:
        communityURL = community?.mapLink || "";
        break;
      case WARNING_TYPE.COMMUNITY_GUIDE:
        communityURL = community?.guideLink || "";
        break;
    }
    analytics?.track(`cta_${warning?.toLowerCase()}`);
    window.open(communityURL, "_blank");
    setWarning(null);
  };

  return (
    <>
      {!!warning && (
        <TabLeavingWarning
          onClose={() => setWarning(null)}
          message={`Please remember to return to this tab to continue your ${getTourLabel(community, CommunityTours.SGT)}.`}
          ConfirmButton={() => (
            <PeekButton
              fullWidth
              onClick={onWarningConfirm}
              startIcon={<ArrowSquareOut height={17} width={17} />}
              variant="contained"
              color="primary"
            >
              {`View Community ${warning === WARNING_TYPE.COMMUNITY_MAP ? "Map" : "Guide"}`}
            </PeekButton>
          )}
        />
      )}
      <PeekStack
        direction={"row"}
        spacing={theme.spacing(1.2)}
        sx={{ position: "absolute", top: 0, right: "10px", zIndex: 3 }}
      >
        {community?.guideLink && (
          <CTAButton
            label="Guidebook"
            onClick={() => setWarning(WARNING_TYPE.COMMUNITY_GUIDE)}
            startIcon={<BookOpen height={19} width={19} />}
          />
        )}
        {community?.mapLink && (
          <CTAButton
            label="Map"
            onClick={() => setWarning(WARNING_TYPE.COMMUNITY_MAP)}
            startIcon={<MapTrifold height={19} width={19} />}
          />
        )}
      </PeekStack>
    </>
  );
};

export default CommunityCTA;
