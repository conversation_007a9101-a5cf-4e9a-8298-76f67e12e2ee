import { BookOpen, MapTrifold } from "@piiqtechnologies/svg-icons";
import { PeekButton, PeekStack, useTheme } from "@piiqtechnologies/ui-components";
import { useAnalytics } from "@utils";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout";
import React from "react";

interface CTAButtonProps {
  label: string;
  onClick: () => void;
  startIcon?: React.ReactNode;
}

const CTAButton = ({ label, onClick, startIcon }: CTAButtonProps) => {
  const theme = useTheme();
  return <PeekButton
    onClick={onClick}
    startIcon={startIcon}
    sx={{
      borderRadius: theme.spacing(1.5),
      height: theme.spacing(2.8),
      width: "100%",
      boxShadow: "none",
      textTransform: "none",
    }}
  >
    {label}
  </PeekButton>;
};

export const CommunityCTA = () => {
  const theme = useTheme();
  const { community } = useScheduleTour();
  const analytics = useAnalytics();

  const onGuidebookClick = () => {
    analytics?.track("cta_guidebook");
    window.open(community?.guideLink, "_blank");
  };
  
  const onMapClick = () => {
    analytics?.track("cta_map");
    window.open(community?.mapLink, "_blank");
  };


  return <PeekStack
    direction={"row"}
    spacing={theme.spacing(2)}
    sx={{
      position: "absolute",
      top: 0, left: 0, right: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      boxShadow: "0px 2px 4px #ADADAD21",
      border: `1px solid ${theme.palette.grey[200]}`,
      backgroundColor: theme.palette.common.white,
    }}
  >
    <CTAButton label="Guidebook" onClick={onGuidebookClick} startIcon={<BookOpen height={19} width={19} />}/>
    <CTAButton label="Map" onClick={onMapClick} startIcon={<MapTrifold height={19} width={19} />}/>
  </PeekStack>;
};

export default CommunityCTA;
