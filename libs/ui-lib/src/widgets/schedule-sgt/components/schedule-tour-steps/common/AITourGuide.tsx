import { <PERSON>et, MessageCircle, PaperPlaneTilt, Question, Sparkle } from "@piiqtechnologies/svg-icons";
import {
  PeekBox,
  PeekButton,
  PeekListItemButton,
  PeekListItemIcon,
  PeekListItemText,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import React, { MemoExoticComponent, SVGProps, useCallback, useState } from "react";

import { CASA_FEATURES, Prospect } from "@types";
import { useAnalytics } from "@utils";
import { CASAChatType, CASAChat as ChatWidget, PromptType } from "@widgets/components/casa-chat";
import PromptText from "@widgets/components/casa-chat/PromptText";
import ClickableEdgeDrawer from "@widgets/components/clickable-drawer/ClickableEdgeDrawer";

export interface CTAMenuItemProps {
  onClick: () => void;
  primaryText: string;
  Icon: MemoExoticComponent<(props: SVGProps<SVGSVGElement>) => JSX.Element>;
}

const CTAMenuItem: React.FC<CTAMenuItemProps> = ({ onClick, primaryText, Icon }) => {
  return (
    <PeekListItemButton
      data-testid="cta-menu-item"
      sx={{ px: 0, py: 2, gap: 1.5, ":hover": { bgcolor: "transparent" } }}
      onClick={onClick}
    >
      <PeekListItemIcon color="primary">
        <Icon height={19} width={19} />
      </PeekListItemIcon>
      <PeekListItemText
        primary={primaryText}
        primaryTypographyProps={{
          color: "primary",
          variant: "h3",
        }}
      />
    </PeekListItemButton>
  );
};

enum CASAErrorLink {
  CALL_AGENT = "call_agent",
}

function* promptGenerator(prospect: Prospect) {
  yield [
    PromptType.TEXT,
    `Hello${
      prospect?.firstName ? ` ${prospect?.firstName}` : ""
    }, I am your AI tour guide, I hope you are enjoying your tour. What can I help you with today?`,
  ];
  return;
}

const AITourGuide = () => {
  const { tourDetails, community } = useScheduleTour();
  const theme = useTheme();
  const analytics = useAnalytics();
  const user = analytics?.user();
  const [openChat, setOpenChat] = useState(false);
  const isCASAEnabled = community.features?.includes(CASA_FEATURES.SGT_CASA);
  const prospect = tourDetails?.verifiedPerson?.prospect as Prospect;
  const sessionId = analytics?.getState()?.context?.sessionId;

  const getChatButtonStyles = useCallback(() => {
    if (!openChat) {
      return {
        height: "32px !important",
        maxWidth: "-webkit-fill-available",
        borderRadius: "40px",
        backgroundImage: `
          linear-gradient(0deg,${theme.palette.common.white}, ${theme.palette.common.white}),
          linear-gradient(142.32deg, ${theme.palette.secondary.main} -8.3%, ${theme.palette.primary.main} 130.23%)`,
        backgroundOrigin: "border-box",
        backgroundClip: "content-box, border-box",
        "&.Mui-focused": {
          borderColor: "transparent",
        },
        border: `1px solid ${theme.palette.grey[200]}`,
        color: theme.palette.grey[200],
        marginTop: theme.spacing(2.5),
        marginX: theme.spacing(1.6),
        marginBottom: theme.spacing(1.6),
        justifyContent: "space-between",
        paddingX: theme.spacing(1),
      };
    }

    return {
      height: "auto !important",
      px: 2,
      pb: 1.8,
      pt: 1.8,
      ...theme.typography.h3,
      fontWeight: "fontWeightMedium",
      justifyContent: "flex-start",
    };
  }, [openChat]);

  const toggleChat = () => {
    setOpenChat((openChat) => !openChat);
    analytics?.track(`casa_chat_${openChat ? "closed" : "opened"}`);
  };

  const onCallAgentClick = () => {
    analytics?.track("cta_call_agent");
    document.location.href = `tel:${community?.sgtSupportPhoneNumber}`;
  };

  const handleChatURL = (href: string) => {
    // Add additional conditions for other URLs as needed
    analytics?.track("casa_chat_link_clicked", {
      href,
    });
  };

  const handleChatLinks = (event: React.MouseEvent) => {
    if (event.target instanceof HTMLAnchorElement) {
      // Handle the click on <a> tags
      const href = event.target.getAttribute("href") as string;
      handleChatURL(href);
    }
    if (event.target instanceof HTMLHeadingElement) {
      const linkId = event.target.getAttribute("itemid");
      switch (linkId) {
        case CASAErrorLink.CALL_AGENT:
          event.preventDefault();
          onCallAgentClick();
          break;
        default:
          handleChatURL(linkId as string);
          window.open(linkId, "_blank");
          break;
      }
    }
  };

  return (
    <>
      <ClickableEdgeDrawer
        onToggle={isCASAEnabled ? setOpenChat : undefined}
        eventIdentification={"casa_chat_drawer"}
        customBleedingArea={94}
        disableExpand={!isCASAEnabled}
        DrawerBleedingContent={() => {
          if (!isCASAEnabled && !!community?.sgtSupportPhoneNumber) {
            return (
              <PeekStack direction={"row"} width={"auto"} alignItems={"center"} justifyContent={"space-between"} p={2}>
                <PeekStack direction={"row"} spacing={0.4}>
                  <PeekTypography variant="h3" lineHeight={"10px"}>
                    <Question height={21} width={21} />
                  </PeekTypography>
                  <PeekTypography variant="h3">Need Help?</PeekTypography>
                </PeekStack>

                <PeekButton
                  startIcon={<Headset height={19} width={19} />}
                  variant="outlined"
                  onClick={onCallAgentClick}
                  sx={{
                    pl: theme.spacing(1.4),
                    height: `${theme.spacing(3.2)} !important`,
                    borderRadius: theme.spacing(2),
                    borderColor: theme.palette.grey[100],
                  }}
                >
                  Speak to an agent
                </PeekButton>
              </PeekStack>
            );
          }

          if (!openChat) {
            return (
              <PeekBox sx={{ p: 2, zIndex: 2 }}>
                <PromptText
                  onChange={toggleChat}
                  responseLoading={false}
                  placeholder={"Have a question? Ask here!"}
                  startAdornment={
                    <PeekTypography
                      sx={{
                        ml: 1.2,
                        lineHeight: "10px",
                      }}
                      color={theme.palette.grey[200]}
                    >
                      <Sparkle height={19} width={19} />
                    </PeekTypography>
                  }
                />
              </PeekBox>
            );
          }

          return (
            <PeekButton
              fullWidth
              sx={getChatButtonStyles()}
              endIcon={!openChat ? <PaperPlaneTilt height={19} width={19} /> : null}
              startIcon={(!openChat && <Question height={19} width={19} />) || <MessageCircle height={19} width={19} />}
            >
              {(!openChat && "Have a question? Ask here!") || "Chat with us"}
            </PeekButton>
          );
        }}
      >
        {openChat && (
          <ChatWidget
            anonymousId={user?.anonymousId as string}
            prospectId={user?.userId as string}
            communityId={community?._id as string}
            spaceId={tourDetails?._id as string}
            sessionId={sessionId as string}
            prompts={promptGenerator(prospect)}
            sxProps={{ p: 0, mr: -2.8 }}
            type={CASAChatType.SGT}
            errorPrompt={[
              {
                type: PromptType.TEXT,
                message:
                  "I apologize for the inconvenience. Our chat service is temporarily unavailable. Please try the following instead",
              },
              {
                type: PromptType.SUGGESTION,
                message: "Speak to an agent",
                linkId: CASAErrorLink.CALL_AGENT,
              },
            ]}
            processChatLink={handleChatLinks}
          />
        )}
      </ClickableEdgeDrawer>
    </>
  );
};

export default AITourGuide;
