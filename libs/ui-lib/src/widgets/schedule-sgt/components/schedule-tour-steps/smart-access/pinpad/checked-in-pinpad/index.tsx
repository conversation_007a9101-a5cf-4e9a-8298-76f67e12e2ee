import { PeekB<PERSON>on, PeekStack } from "@piiqtechnologies/ui-components";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { ScheduleTourStep } from "@widgets/schedule-sgt/types";
import { getFormattedTourTimeSlot } from "@widgets/schedule-sgt/utils";
import React from "react";
import AITourGuide from "../../../common/AITourGuide";
import CommunityCTA from "../../../common/CommunityCTA";
import TourRemainingTime from "../../../common/TourRemainingTime";
import TourTimeItem from "../../../common/TourTimeItem";
import TourInstructions from "../../common/TourInstructions";

export interface CheckedInPinpadProps { }

export const CheckedInPinpad: React.FC<CheckedInPinpadProps> = () => {
  const { tourDetails, setScheduleTourStep } = useScheduleTour();

  function handleGetAccessDetails() {
    setScheduleTourStep(ScheduleTourStep.PINPAD_INSTRUCTIONS);
  }

  return (
    <>
      <PeekStack direction="column" justifyContent="space-between" height="100%" spacing={3}>
        <PeekStack direction="column" spacing={2}>
          <TourInstructions title={`Make your way to unit ${tourDetails?.space?.unit}`} />
        </PeekStack>

        <PeekStack direction="column" spacing={1}>
          <PeekStack spacing={1} direction="row">
            <TourTimeItem
              label="START TIME"
              value={getFormattedTourTimeSlot(tourDetails?.startMinute || 0, tourDetails?.timeZone || "")}
            />
            <TourTimeItem
              label="END TIME"
              value={getFormattedTourTimeSlot(tourDetails?.endMinute || 0, tourDetails?.timeZone || "")}
            />
          </PeekStack>

          <TourRemainingTime />
        </PeekStack>

        <PeekButton variant="contained" onClick={handleGetAccessDetails} data-testid="get-access-code-button">
          Unlock the Door
        </PeekButton>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />

      {/* COMMUNITY CTA */}
      <CommunityCTA />
    </>
  );
};

export default CheckedInPinpad;
