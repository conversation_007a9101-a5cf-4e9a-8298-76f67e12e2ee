import { useUpdateWalkabout } from "@apis";
import { PeekP<PERSON>, PeekStack, useTheme } from "@piiqtechnologies/ui-components";
import { AccessDeviceTypes } from "@types";
import { useError, useLoader } from "@utils";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { ScheduleTourStep } from "@widgets/schedule-sgt/types";
import AITourGuide from "../../../common/AITourGuide";
import MultilineText from "../../../common/MultilineText";
import { KeyValueItem } from "../../pinpad/tour-details";
import DeviceSteps from "../DeviceSteps";
import React, { useMemo, useEffect } from "react";

interface DeviceInformationProps {}

const DeviceInstructions: React.FC<DeviceInformationProps> = () => {
  const { tourDetails, community, setScheduleTourStep, setTourDetails, tourToken } = useScheduleTour();
  const [updateWalkabout, walkaboutUpdated, walkaboutUpdating, updateError] = useUpdateWalkabout();
  const theme = useTheme();
  const { setLoader } = useLoader();
  const { setError } = useError();

  const isPerimeterAccess = useMemo(() => {
    if (typeof tourDetails.accessDeviceIndex === "number") {
      return tourDetails.accessDeviceIndex > 0;
    }
    return false;
  }, [tourDetails]);

  const handleLastStep = async () => {
    if (tourDetails && tourDetails.accessDevices && typeof tourDetails.accessDeviceIndex === "number") {
      const deviceType = tourDetails?.accessDevices[tourDetails.accessDeviceIndex]?.device?.type;

      if (isPerimeterAccess) {
        if (deviceType !== AccessDeviceTypes.CONCIERGE) {
          setScheduleTourStep(ScheduleTourStep.CHECK_IN);
        }
      } else {
        if (deviceType === AccessDeviceTypes.KEYBOX) {
          setScheduleTourStep(ScheduleTourStep.KEYBOX_TOUR_DETAILS);
        } else {
          setScheduleTourStep(ScheduleTourStep.PINPAD_TOUR_DETAILS);
        }
      }

      setTourDetails({
        ...tourDetails,
        accessDeviceIndex: isPerimeterAccess ? tourDetails.accessDeviceIndex - 1 : 0,
      });

      if (tourDetails.accessDeviceIndex === 0) {
        await updateWalkabout(
          tourDetails._id,
          {
            completedAt: new Date().toUTCString(),
          },
          tourToken,
        );
      }
    }
  };

  const accessCode = useMemo(() => {
    if (tourDetails.accessDevices && typeof tourDetails.accessDeviceIndex === "number") {
      return tourDetails?.accessDevices[tourDetails.accessDeviceIndex]?.accessCode || "";
    }
    return "";
  }, [tourDetails]);

  useEffect(() => {
    if (updateError) {
      setError(updateError.message);
    }
  }, [updateError]);

  useEffect(() => {
    setLoader(walkaboutUpdating);
    return () => setLoader(false);
  }, [walkaboutUpdating]);

  return (
    <>
      <PeekStack direction="column" justifyContent="space-between" height="100%" spacing={4.5}>
        <PeekStack direction="column" spacing={2.5}>
          <MultilineText isTitle>
            Access {isPerimeterAccess ? `the Community` : `Unit ${tourDetails?.space?.unit}`}
          </MultilineText>

          <PeekStack direction="column" spacing={theme.spacing(1)}>
            {/* ACCESS CODE */}
            <PeekPaper elevation={1} sx={{ px: theme.spacing(2), py: theme.spacing(1.2), alignItems: "center" }}>
              <KeyValueItem keyText="ACCESS CODE" value={accessCode} valueSize="h2" />
            </PeekPaper>

            {/* DEVICE STEPS */}
            <DeviceSteps handleLastStep={handleLastStep} />
          </PeekStack>
        </PeekStack>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default DeviceInstructions;
