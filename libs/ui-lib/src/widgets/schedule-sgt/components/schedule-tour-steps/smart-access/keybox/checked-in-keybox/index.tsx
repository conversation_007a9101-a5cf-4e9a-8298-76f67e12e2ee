import React, { useEffect } from "react";

import { Peek<PERSON><PERSON><PERSON>, PeekStack } from "@piiqtechnologies/ui-components";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { ScheduleTourStep } from "@widgets/schedule-sgt/types";

import AITourGuide from "../../../common/AITourGuide";
import MultilineText from "../../../common/MultilineText";
import SmartAccessTourInformation from "../../common/SmartAccessTourInformation";

export interface CheckedInSmartAccessProps {}

export const CheckedInKeybox: React.FC<CheckedInSmartAccessProps> = () => {
  const { tourDetails, setScheduleTourStep, setIdvCompleted } = useScheduleTour();

  useEffect(() => {
    setIdvCompleted(true);
  }, []);

  function handleGetAccessDetails() {
    setScheduleTourStep(ScheduleTourStep.KEYBOX_INSTRUCTIONS);
  }

  return (
    <>
      <PeekStack direction="column" justifyContent="space-between" height="100%" spacing={3}>
        <PeekStack direction="column" spacing={1.2}>
          <MultilineText isTitle>Grab the unit key</MultilineText>
          <MultilineText>
            The unit key is inside a {tourDetails?.accessDevices?.[tourDetails.accessDeviceIndex!]?.device.type}. Find
            the correct {tourDetails?.accessDevices?.[tourDetails.accessDeviceIndex!]?.device.type} and tap on
            &ldquo;Get Access Code&rdquo; to open it.
          </MultilineText>
        </PeekStack>

        {/* KEYBOX INFO */}
        <SmartAccessTourInformation />

        <PeekButton variant="contained" onClick={handleGetAccessDetails} data-testid="get-access-code-button">
          Get Access Code
        </PeekButton>
      </PeekStack>
      
      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default CheckedInKeybox;
