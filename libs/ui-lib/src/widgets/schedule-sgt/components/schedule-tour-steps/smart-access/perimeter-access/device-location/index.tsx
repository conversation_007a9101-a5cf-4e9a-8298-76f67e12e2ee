import { Peek<PERSON><PERSON><PERSON>, PeekStack, useTheme } from "@piiqtechnologies/ui-components";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { ScheduleTourStep } from "@widgets/schedule-sgt/types";
import React, { useMemo } from "react";
import MultilineText from "../../../common/MultilineText";
import SmartAccessTourInformation from "../../common/SmartAccessTourInformation";
import AITourGuide from "../../../common/AITourGuide";

interface PerimeterAccessDeviceLocationProps {}

const PerimeterAccessDeviceLocation: React.FC<PerimeterAccessDeviceLocationProps> = () => {
  const theme = useTheme();
  const { tourDetails, setScheduleTourStep } = useScheduleTour();

  const handleGetAccessCode = () => {
    setScheduleTourStep(ScheduleTourStep.PERIMETER_ACCESS_DEVICE_INSTRUCTIONS);
  };

  const locationInstructions = useMemo(() => {
    if (!tourDetails?.accessDevices || !(typeof tourDetails?.accessDeviceIndex === "number")) return "";
    return tourDetails?.accessDevices[tourDetails.accessDeviceIndex]?.device.locationInstructions;
  }, [tourDetails]);

  return (
    <>
      <PeekStack direction="column" spacing={theme.spacing(3)} height="100%">
        <PeekStack direction="column" spacing={theme.spacing(1.2)}>
          <MultilineText isTitle>Let&apos;s start your tour</MultilineText>
          <MultilineText>{locationInstructions}</MultilineText>
        </PeekStack>

        {/* TOUR INFO */}
        <SmartAccessTourInformation />

        <PeekButton variant="contained" onClick={handleGetAccessCode} data-testid="get-access-code-button">
          Get Access Code
        </PeekButton>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default PerimeterAccessDeviceLocation;
