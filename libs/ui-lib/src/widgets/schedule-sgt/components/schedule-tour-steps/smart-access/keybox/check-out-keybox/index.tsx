import { styled } from "@mui/material/styles";
import { <PERSON>eek<PERSON><PERSON>, PeekDivider, PeekPaper, PeekStack, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import { Key } from "@piiqtechnologies/svg-icons";
import { TOUR_INDEX, useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import React, { useEffect, useMemo } from "react";
import AITourGuide from "../../../common/AITourGuide";
import Feedback from "../../../common/Feedback";
import MultilineText from "../../../common/MultilineText";
import { KeyValueItem } from "../tour-access-details";
import NextTour from "./NextTour";
import ViewAvailabilities from "../../../common/ViewAvailabilities";

const PeekDividerCustom = styled(PeekDivider)(({ theme }) => ({
  borderColor: theme.palette.grey[200],
  width: "100%",
}));

const CheckOutKeybox = () => {
  const { tourDetails, setTourIndex } = useScheduleTour();
  const theme = useTheme();

  useEffect(() => {
    if (!tourDetails.hasNextTour) {
      setTourIndex(0);
    }
  }, []);

  const accessCode = useMemo(() => {
    if (tourDetails?.accessDevices && typeof tourDetails?.accessDeviceIndex === "number") {
      return tourDetails?.accessDevices[tourDetails?.accessDeviceIndex].accessCode || "";
    }
    return "";
  }, [tourDetails]);

  const getAccessCodeId = () => {
    if (tourDetails?.accessDevices && typeof tourDetails?.accessDeviceIndex === "number") {
      const device = tourDetails.accessDevices[tourDetails.accessDeviceIndex]?.device;
      return `${device?.type} ${device.name}` || "";
    }
    return "";
  };

  return (
    <>
      <PeekStack direction="column" spacing={theme.spacing(2.5)} sx={{ mt: theme.spacing(3) }}>
        <MultilineText isTitle>Thanks for visiting!</MultilineText>
        <PeekStack direction="column" spacing={1.2} data-testid="return-instructions">
          <PeekStack direction="row" spacing={1}>
            <PeekTypography color="grey.300" variant="h3">
              <Key />
            </PeekTypography>
            {/* RETURN INSTRUCTIONS TITLE*/}
            <PeekTypography variant="h4" color="grey.300" fontWeight="fontWeightMedium">
              RETURN INSTRUCTIONS
            </PeekTypography>
          </PeekStack>

          {/* RETURN INSTRUCTIONS */}
          <MultilineText>
            Once you&prime;re done with your tour, don&prime;t forget to use the access code provided to unlock the
            keybox and return the unit key back inside.
          </MultilineText>
        </PeekStack>

        <PeekStack direction="column" spacing={1} data-testid="keybox-info">
          <PeekPaper elevation={1}>
            <PeekStack direction="column" spacing={1}>
              <PeekStack direction="column">
                {/* ACCESS CODE */}
                <PeekBox sx={{ p: theme.spacing(2) }}>
                  <KeyValueItem keyText={"ACCESS CODE"} value={accessCode} valueSize="h2" />
                </PeekBox>
                <PeekDividerCustom orientation="horizontal" />

                {/* KEYBOX ID */}
                <PeekBox sx={{ p: theme.spacing(2) }}>
                  <KeyValueItem keyText={"KEYBOX ID"} value={getAccessCodeId()} valueSize="h2" />
                </PeekBox>
              </PeekStack>
            </PeekStack>
          </PeekPaper>

          {(!!tourDetails.hasNextTour && <NextTour />) || (
            <PeekStack direction="column" spacing={1}>
              <Feedback />
              <ViewAvailabilities />
            </PeekStack>
          )}
        </PeekStack>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default CheckOutKeybox;
