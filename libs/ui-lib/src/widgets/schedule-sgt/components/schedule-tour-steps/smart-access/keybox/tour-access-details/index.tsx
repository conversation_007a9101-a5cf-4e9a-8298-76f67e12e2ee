import { styled } from "@mui/material/styles";
import {
  PeekBox,
  Peek<PERSON>utton,
  PeekDivider,
  PeekPaper,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { AlertCircle } from "@piiqtechnologies/svg-icons";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { ScheduleTourStep } from "@widgets/schedule-sgt/types";
import { getFormattedTourTimeSlot } from "@widgets/schedule-sgt/utils";
import React from "react";
import AITourGuide from "../../../common/AITourGuide";
import TourRemainingTime from "../../../common/TourRemainingTime";
import TourInstructions from "../../common/TourInstructions";

interface KeyValueItemProps {
  keyText: string;
  value: string;
  valueSize: "h2" | "h4";
}

export const KeyValueItem = ({ keyText: key, value, valueSize }: KeyValueItemProps) => {
  const theme = useTheme();
  return (
    <PeekStack direction="row" justifyContent="space-between" alignItems="center">
      <PeekTypography variant="h4" color={theme.palette.grey[400]} fontWeight="fontWeightMedium">
        {key}
      </PeekTypography>
      <PeekTypography variant={valueSize} fontWeight="fontWeightMedium">
        {value}
      </PeekTypography>
    </PeekStack>
  );
};

export const PeekDividerCustom = styled(PeekDivider)(({ theme }) => ({
  borderColor: theme.palette.grey[200],
  width: "100%",
}));

export interface TourAccessDetailsProps {}

export const TourAccessDetails: React.FC<TourAccessDetailsProps> = () => {
  const { tourDetails, setScheduleTourStep } = useScheduleTour();
  const theme = useTheme();

  const handleReturnUnitKeys = async () => {
    setScheduleTourStep(ScheduleTourStep.KEYBOX_CHECK_OUT);
  };

  return (
    <>
      <PeekStack direction="column">
        <PeekPaper elevation={1}>
          <PeekStack direction="column">
            <PeekStack direction="column">
              <TourRemainingTime hideBackground />
              <PeekDividerCustom orientation="horizontal" />
              <PeekBox sx={{ p: theme.spacing(2) }}>
                <KeyValueItem
                  keyText={"END TIME"}
                  value={getFormattedTourTimeSlot(tourDetails?.endMinute as number, tourDetails?.timeZone || "")}
                  valueSize="h4"
                />
              </PeekBox>
            </PeekStack>
          </PeekStack>
        </PeekPaper>

        <TourInstructions
          title={`Tour of unit ${
            tourDetails.space?.community.displayBuildingName && tourDetails?.space?.building?.name
              ? `${tourDetails.space?.building.name}-${tourDetails?.space?.unit}`
              : tourDetails?.space?.unit
          }`}
        />

        {/* RETURN KEY ALERT */}
        <PeekStack direction="column" spacing={1.2} sx={{ mt: theme.spacing(3.8) }}>
          {/* ALERT */}
          <PeekPaper elevation={1} sx={{ py: theme.spacing(1.3), px: theme.spacing(2.2) }}>
            <PeekStack direction="row" spacing={theme.spacing(1.2)} alignItems="center">
              <PeekTypography variant="h2" color={theme.palette.secondary.main} textAlign="center">
                <AlertCircle />
              </PeekTypography>
              <PeekTypography variant="h4">Please return the key before leaving the property</PeekTypography>
            </PeekStack>
          </PeekPaper>

          {/* ACTION BUTTON */}
          <PeekButton
            variant="contained"
            color="secondary"
            fullWidth
            sx={{ mb: `${theme.spacing(3.8)} !important`, fontWeight: theme.typography.fontWeightMedium }}
            onClick={() => handleReturnUnitKeys()}
          >
            Return the unit key
          </PeekButton>
        </PeekStack>
      </PeekStack>

      {/* CONTACT AGENT */}
      <AITourGuide />
    </>
  );
};

export default TourAccessDetails;
