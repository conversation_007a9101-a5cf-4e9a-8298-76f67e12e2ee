import { PeekStack, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import { TOUR_INDEX, useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import React, { useEffect, useMemo } from "react";
import AITourGuide from "../../../common/AITourGuide";
import Feedback from "../../../common/Feedback";
import MultilineText from "../../../common/MultilineText";
import ReminingAndEndTime from "../../common/ReminderAndEndTime";
import NextTour from "../../keybox/check-out-keybox/NextTour";
import ViewAvailabilities from "../../../common/ViewAvailabilities";

interface KeyValueItemProps {
  keyText: string;
  value: string;
  valueSize: "h2" | "h4";
}

export const KeyValueItem = ({ keyText: key, value, valueSize }: KeyValueItemProps) => {
  const theme = useTheme();
  return (
    <PeekStack direction="row" justifyContent="space-between" alignItems="center">
      <PeekTypography variant="h4" color={theme.palette.grey[400]} fontWeight="fontWeightMedium">
        {key}
      </PeekTypography>
      <PeekTypography variant={valueSize} fontWeight="fontWeightMedium">
        {value}
      </PeekTypography>
    </PeekStack>
  );
};

export interface TourAccessDetailsProps {}

export const PinpadTourAccessDetails: React.FC<TourAccessDetailsProps> = () => {
  const { tourDetails, setTourIndex } = useScheduleTour();
  const theme = useTheme();

  useEffect(() => {
    if (!tourDetails.hasNextTour) {
      setTourIndex(0);
    }
  }, []);

  const unitText = useMemo(() => {
    return `Unit ${
      tourDetails?.space?.community.displayBuildingName
        ? `${tourDetails?.space?.building.name}-${tourDetails?.space?.unit}`
        : tourDetails?.space?.unit
    }`;
  }, [tourDetails]);

  return (
    <>
      <PeekStack direction="column" spacing={theme.spacing(3)}>
        <MultilineText isTitle>Tour of unit {unitText}</MultilineText>

        <MultilineText>
          Welcome to {unitText}. Once you&apos;re done with your tour, please remember to grab all belongings and close
          the door on your way out. We hope you enjoyed your visit.
        </MultilineText>

        <PeekStack direction="column" spacing={theme.spacing(1)}>
          {/* REMAINING/END TIME */}
          <ReminingAndEndTime />

          {/* FEEDBACK */}
          {(!!tourDetails.hasNextTour && <NextTour />) || (
            <PeekStack direction="column" spacing={1}>
              <Feedback />
              <ViewAvailabilities />
            </PeekStack>
          )}
        </PeekStack>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default PinpadTourAccessDetails;
