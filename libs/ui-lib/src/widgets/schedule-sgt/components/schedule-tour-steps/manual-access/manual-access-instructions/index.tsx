import { PeekStack } from "@piiqtechnologies/ui-components";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { getFormattedTourTimeSlot } from "@widgets/schedule-sgt/utils";
import React from "react";
import AITourGuide from "../../common/AITourGuide";
import Feedback from "../../common/Feedback";
import TourRemainingTime from "../../common/TourRemainingTime";
import TourTimeItem from "../../common/TourTimeItem";
import TourInstructions from "../../smart-access/common/TourInstructions";
import ViewAvailabilities from "../../common/ViewAvailabilities";

const ManualTourInstruction = () => {
  const { tourDetails } = useScheduleTour();

  return (
    <>
      <PeekStack direction="column" spacing={3} justifyContent="center">
        <TourInstructions
          title={`Tour of unit ${
            tourDetails.space?.community?.displayBuildingName && tourDetails?.space?.building?.name
              ? `${tourDetails.space?.building.name}-${tourDetails?.space?.unit}`
              : tourDetails?.space?.unit
          }`}
        />

        <PeekStack direction="column" spacing={1}>
          <PeekStack spacing={1} direction="row">
            <TourTimeItem
              label="START TIME"
              value={getFormattedTourTimeSlot(tourDetails?.startMinute || 0, tourDetails?.timeZone || "")}
            />
            <TourTimeItem
              label="END TIME"
              value={getFormattedTourTimeSlot(tourDetails?.endMinute || 0, tourDetails?.timeZone || "")}
            />
          </PeekStack>

          <TourRemainingTime />

          <Feedback />
          <ViewAvailabilities />
        </PeekStack>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default ManualTourInstruction;
