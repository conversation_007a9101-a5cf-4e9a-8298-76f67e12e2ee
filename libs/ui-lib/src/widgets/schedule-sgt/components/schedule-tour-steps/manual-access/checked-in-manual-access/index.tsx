import React, { useCallback, useEffect } from "react";

import { useUpdateWalkabout } from "@apis";
import { AccessDeviceTypes } from "@types";
import { useError, useLoader } from "@utils";
import { styled } from "@mui/material/styles";
import {
  PeekBox,
  PeekButton,
  PeekChip,
  PeekDivider,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { ShieldCheck } from "@piiqtechnologies/svg-icons";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";
import { ScheduleTourStep } from "@widgets/schedule-sgt/types";
import { getFormattedTourTimeSlot } from "@widgets/schedule-sgt/utils";

import AITourGuide from "../../common/AITourGuide";
import MultilineText from "../../common/MultilineText";
import TourRemainingTime from "../../common/TourRemainingTime";
import TourTimeItem from "../../common/TourTimeItem";

const StyledChip = styled(PeekChip)(({ theme }) => ({
  backgroundColor: theme.palette.common.white,
  height: 20,
  color: theme.palette.success.main,
  padding: 6,
  "> p": { fontSize: theme.spacing(1.6) },
  "> span": { fontSize: theme.spacing(1.2) },
}));

const PeekDividerCustom = styled(PeekDivider)(({ theme }) => ({
  borderColor: theme.palette.common.white,
  width: "100%",
}));

interface CheckInManualAccessProps {}

export const CheckInManualAccess: React.FC<CheckInManualAccessProps> = () => {
  const [updateWalkabout, , walkaboutUpdating, updateError] = useUpdateWalkabout();
  const { tourDetails, setTourDetails, setIdvCompleted, setScheduleTourStep, tourToken } = useScheduleTour();
  const theme = useTheme();
  const { setLoader } = useLoader();
  const { setError } = useError();

  useEffect(() => {
    setIdvCompleted(true);
  }, []);

  const getNextTourStep = useCallback(
    (nextTourStep: number) => {
      if (!tourDetails?.accessDeviceIndex || !tourDetails.accessDevices) return ScheduleTourStep.CHECK_IN;

      switch (tourDetails?.accessDevices[nextTourStep]?.device?.type) {
        case AccessDeviceTypes.KEYBOX:
          return ScheduleTourStep.KEYBOX_CHECKED_IN;

        case AccessDeviceTypes.PINPAD:
          return ScheduleTourStep.PINPAD_CHECKED_IN;

        case AccessDeviceTypes.CONCIERGE:
          return ScheduleTourStep.MANUAL_ACCESS_CHECKED_IN;

        default:
          return ScheduleTourStep.CHECK_IN;
      }
    },
    [tourDetails],
  );

  const handleStarTour = useCallback(async () => {
    if (tourDetails.accessDeviceIndex === 0) {
      await updateWalkabout(
        tourDetails._id,
        {
          completedAt: new Date().toUTCString(),
        },
        tourToken,
      );
    }

    if (typeof tourDetails?.accessDeviceIndex === "number") {
      const nextDeviceIndex = tourDetails?.accessDeviceIndex > 0 ? tourDetails.accessDeviceIndex - 1 : 0;

      setTourDetails({
        ...tourDetails,
        accessDeviceIndex: nextDeviceIndex,
      });

      if (tourDetails.accessDeviceIndex === 0) {
        setScheduleTourStep(ScheduleTourStep.MANUAL_ACCESS_TOUR_INSTRUCTIONS);
      } else {
        setScheduleTourStep(getNextTourStep(nextDeviceIndex));
      }
    }
  }, [tourDetails, getNextTourStep]);

  useEffect(() => {
    if (updateError) {
      setError(updateError.message);
    }
  }, [updateError]);

  useEffect(() => {
    setLoader(walkaboutUpdating);
    return () => setLoader(false);
  }, [walkaboutUpdating]);

  return (
    <>
      <PeekStack direction="column" spacing={3} justifyContent="center">
        <PeekStack direction="column" spacing={1}>
          <MultilineText isTitle>
            {!!tourDetails?.accessDevices && typeof tourDetails?.accessDeviceIndex === "number"
              ? `Show this screen to the ${tourDetails?.accessDevices[tourDetails?.accessDeviceIndex]?.device?.name}`
              : "-"}
          </MultilineText>
          <MultilineText>
            You are checked-in! Tap on &apos;Start Tour&apos; once you get the unit key from the{" "}
            {!!tourDetails?.accessDevices?.length ? tourDetails?.accessDevices[0]?.device?.name : "concierge"}.
          </MultilineText>
        </PeekStack>

        <PeekStack direction="column" spacing={1}>
          <PeekBox sx={{ backgroundColor: "success.main" }} borderRadius={2} data-testid="check-in-info">
            <PeekStack direction="column" justifyContent="space-between" p={2}>
              <PeekStack spacing={1} direction="row" justifyContent="space-between" alignItems="flex-start">
                <MultilineText fontWeight="fontWeightMedium" color="common.white">
                  NAME
                </MultilineText>
                <StyledChip
                  icon={
                    <PeekTypography color="green">
                      <ShieldCheck />
                    </PeekTypography>
                  }
                  label="ID Verified"
                  size="small"
                  sx={{ m: 0 }}
                />
              </PeekStack>

              <MultilineText isTitle color="common.white">
                {tourDetails?.verifiedPerson?.prospect?.firstName} {tourDetails?.verifiedPerson?.prospect?.lastName}
              </MultilineText>
            </PeekStack>

            <PeekDividerCustom orientation="horizontal" />

            <PeekStack spacing={3} direction="row" justifyContent="space-between" p={2} alignItems="center">
              <PeekTypography
                color="common.white"
                whiteSpace={(tourDetails?.space?.unit?.length as number) <= 6 ? "nowrap" : "normal"}
                fontWeight="fontWeightMedium"
                fontSize={45}
                lineHeight={theme.spacing(4.5)}
              >
                {tourDetails?.space?.community?.displayBuildingName
                  ? `${tourDetails?.space?.building?.name}-${tourDetails?.space?.unit}`
                  : tourDetails?.space?.unit}
              </PeekTypography>
              <PeekTypography variant="h4" color="common.white" fontWeight="fontWeightMedium">
                Please provide the keys for this unit
              </PeekTypography>
            </PeekStack>
          </PeekBox>

          <PeekStack spacing={1} direction="row" data-testid="start-end-times">
            <TourTimeItem
              label="START TIME"
              value={getFormattedTourTimeSlot(tourDetails?.startMinute, tourDetails?.timeZone || "")}
            />
            <TourTimeItem
              label="END TIME"
              value={getFormattedTourTimeSlot(tourDetails?.endMinute || 0, tourDetails?.timeZone || "")}
            />
          </PeekStack>

          <TourRemainingTime />
        </PeekStack>

        <PeekButton variant="contained" fullWidth onClick={() => handleStarTour()}>
          {typeof tourDetails?.accessDeviceIndex === "number"
            ? tourDetails?.accessDeviceIndex === 1
              ? "Next Step"
              : "Start Tour"
            : "-"}
        </PeekButton>
      </PeekStack>

      {/* AI TOUR AGENT */}
      <AITourGuide />
    </>
  );
};

export default CheckInManualAccess;
