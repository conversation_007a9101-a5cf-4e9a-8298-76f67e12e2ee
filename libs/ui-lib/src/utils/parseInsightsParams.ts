import { InsightsFilters, PropertiesAveragesFilters } from "@types";
import { format, parseISO, isValid } from "date-fns";

export const parseParams = (filters?: InsightsFilters | PropertiesAveragesFilters) => {
  let propertyParams: any = { ...filters };

  if (propertyParams.layout) {
    propertyParams.bedrooms = filters.layout.map((layout) => layout.split(" ")[0]);
    delete propertyParams.layout;
  }

  Object.keys(propertyParams).forEach((key) => {
    if (
      propertyParams[key] === null ||
      propertyParams[key] === undefined ||
      propertyParams[key] === "" ||
      (Array.isArray(propertyParams[key]) && propertyParams[key].length === 0) ||
      (key === "dateRange" &&
        propertyParams[key] &&
        (propertyParams[key].startDate === null || propertyParams[key].endDate === null))
    ) {
      delete propertyParams[key];
    }
  });

  return propertyParams;
};
