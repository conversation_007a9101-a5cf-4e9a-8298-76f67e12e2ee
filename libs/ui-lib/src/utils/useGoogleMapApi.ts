import { loadScript } from "./scriptUtils";
import { useEffect, useRef, useState } from "react";

export const useGoogleMapApi = () => {
  const loadedGoogleMapScript = useRef(false);
  const [googleMapApiLoaded, setGoogleMapApiLoaded] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined" && !loadedGoogleMapScript.current) {
      if (!document.querySelector("#google-maps")) {
        loadScript(
          `https://maps.googleapis.com/maps/api/js?key=${process.env.PEEK_APP_GOOGLE_MAPS_API_KEY}&v=${process.env.PEEK_APP_GOOGLE_MAPS_API_VERSION}&libraries=places,geometry`,
          document.querySelector("head"),
          "google-maps",
          () => {
            setGoogleMapApiLoaded(true);
          },
        );
      }
      loadedGoogleMapScript.current = true;
    }
  }, []);

  return {
    mapAPiloaded: googleMapApiLoaded,
  };
};
