import { ScanRequestResponse, getScanRequests } from "@services";
import dot from "dot-object";
import { useState } from "react";
import useAbortController from "../useAbortController";

export interface ScanRequestsParams {
  order?: "asc" | "desc";
  communityId?: string;
  orderBy?: string;
  limit?: number;
  offset?: number;
}

const useScanRequests = () => {
  const [scanRequests, setScanRequests] = useState<ScanRequestResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (scanRequestsParams?: ScanRequestsParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setScanRequests(null);
      setError(null);

      const formattedParams = scanRequestsParams && dot.dot(scanRequestsParams);
      const scanRequests = (await getScanRequests({
        params: formattedParams,
        signal: controller.current.signal,
      })) as unknown as ScanRequestResponse;

      setScanRequests(scanRequests);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, scanRequests, loading, error];
};

export { useScanRequests };
