import { bulkUpdateScanRequest, ScanRequest, ScanRequestResponse } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";

const useBulkUpdateScanRequests = () => {
  const [updatedScanRequests, setUpdatedScanRequests] = useState<ScanRequestResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (scanRequests: ScanRequest[]) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setUpdatedScanRequests(null);
      setError(null);
      const res = await bulkUpdateScanRequest({ scanRequests }, { signal: controller.current.signal });
      setUpdatedScanRequests(res as unknown as ScanRequestResponse);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    } finally {
      setLoading(false);
    }
  };

  return [api, updatedScanRequests, loading, error];
};

export { useBulkUpdateScanRequests };
