import { useState } from "react";
import { LoginResponse, login } from "@services";

export interface LoginData {
  email: string;
  password: string;
  origin?: string;
}

const useLogin = () => {
  const [loggedIn, setLoggedIn] = useState<LoginResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const api = async (loginData: LoginData) => {
    try {
      const { email, password, origin } = loginData;
      setLoading(true);
      setLoggedIn(null);
      setError(null);
      const res = (await login({
        email,
        password,
        origin,
      })) as unknown as LoginResponse;
      setLoggedIn(res);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return [api, loggedIn, loading, error];
};

export { useLogin };
