import { getMostToursData, getTrafficSources, MostToursTimesResponse } from "@services";
import { useEffect, useRef, useState } from "react";
import useAbortController from "../useAbortController";

export type PeriodOption = "time" | "day";

interface MostToursParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
}

const useMostToursByTimeOrDay = () => {
  const [mostToursData, setMostToursData] = useState<MostToursTimesResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (option: PeriodOption, trafficSourcesParams: MostToursParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setMostToursData(null);
      setError(null);

      const mostToursResponse = (await getMostToursData(option, {
        params: trafficSourcesParams,
        signal: controller.current.signal,
      })) as unknown as MostToursTimesResponse;

      setMostToursData(mostToursResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, mostToursData, loading, error];
};

export { useMostToursByTimeOrDay };
