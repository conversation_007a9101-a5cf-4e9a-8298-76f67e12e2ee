import { getViewedSpaces } from "@services";
import { SpaceType, ViewedSpaces } from "@types";
import { useState } from "react";
import useAbortController from "../useAbortController";

interface ViewedSpacesParams {
  spaceType?: SpaceType;
  amount?: number;
  unit?: "minute" | "hour" | "day" | "week" | "month";
  communityId?: string | string[];
  prospectId?: string;
  offset?: number;
  limit?: number;
  orderBy?: string;
  order?: "asc" | "desc";
  groupBy?: "spaceBedrooms" | "spaceBathrooms" | "spaceFloorPlan" | "referrer";
}

const useViewedSpaces = () => {
  const [prospectMostViewedSpaces, setViewedSpaces] = useState<ViewedSpaces | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (viewedSpacesParams: ViewedSpacesParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setViewedSpaces(null);
      setError(null);
      const prospectMostViewedSpacesRes = (await getViewedSpaces({
        params: viewedSpacesParams,
        signal: controller.current.signal,
      })) as unknown as ViewedSpaces;
      setViewedSpaces(prospectMostViewedSpacesRes);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return [api, prospectMostViewedSpaces, loading, error];
};

export { useViewedSpaces };
