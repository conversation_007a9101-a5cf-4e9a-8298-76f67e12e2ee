import { EngagementResponse, getOnlineEngagement } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";

interface OnlineEngagementParams {
  communityIds: string[];
  period: {
    startDate: Date;
    endDate: Date;
  };
}

const useOnlineEngagement = () => {
  const [engagementData, setEngagementData] = useState<EngagementResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (onlineEngagementParams: OnlineEngagementParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setEngagementData(null);
      setError(null);

      const prospectMostViewedSpacesRes = (await getOnlineEngagement({
        params: onlineEngagementParams,
        signal: controller.current.signal,
      })) as unknown as EngagementResponse;

      setEngagementData(prospectMostViewedSpacesRes);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, engagementData, loading, error];
};

export { useOnlineEngagement };
