import { useState } from "react";
import { CommunityComparisonResponse, getCommunititesComparisonData } from "../../services/analyticsService";
import useAbortController from "../useAbortController";
import { ProspectsType } from "./useMostViewed";

export type ComparisonType = "views" | "tours";

export interface CommunitiesComparisonParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
  prospects: ProspectsType;
  sgtStatus?: "completed" | "scheduled";
}

const useCommunitiesComparison = () => {
  const [comparisonData, setComparisonData] = useState<CommunityComparisonResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (option: ComparisonType, mostViewedParams: CommunitiesComparisonParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setComparisonData(null);
      setError(null);

      const comparisonResponse = (await getCommunititesComparisonData(option, {
        params: mostViewedParams,
        signal: controller.current.signal,
      })) as unknown as CommunityComparisonResponse;

      setComparisonData(comparisonResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, comparisonData, loading, error];
};

export { useCommunitiesComparison };
