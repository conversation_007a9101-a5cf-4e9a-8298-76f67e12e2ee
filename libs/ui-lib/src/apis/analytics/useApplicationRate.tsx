import { ApplicationRateResponse, getApplicationRate } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";

interface ApplicationRateParams {
  communityIds: string[];
  period: {
    startDate: string;
    endDate: string;
  };
}

const useApplicationRate = () => {
  const [applicationRateData, setApplicationRateData] = useState<ApplicationRateResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (applicationRateParams: ApplicationRateParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setApplicationRateData(null);
      setError(null);

      const prospectMostViewedSpacesRes = (await getApplicationRate({
        params: applicationRateParams,
        signal: controller.current.signal,
      })) as unknown as ApplicationRateResponse;

      setApplicationRateData(prospectMostViewedSpacesRes);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, applicationRateData, loading, error];
};

export { useApplicationRate };
