import { getTrafficSources, TrafficSourcesResponse } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";

interface TrafficSourcesParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
  app: "sgt" | "virtualTour";
}

const useTrafficSources = () => {
  const [trafficSourceData, setTrafficSourceData] = useState<TrafficSourcesResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (trafficSourcesParams: TrafficSourcesParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setTrafficSourceData(null);
      setError(null);

      const trafficSourcesResponse = (await getTrafficSources({
        params: trafficSourcesParams,
        signal: controller.current.signal,
      })) as unknown as TrafficSourcesResponse;

      setTrafficSourceData(trafficSourcesResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, trafficSourceData, loading, error];
};

export { useTrafficSources };
