import { useState } from "react";
import { CaptureDetailsResponse, getCaptureDetails } from "../../services/analyticsService";
import useAbortController from "../useAbortController";

interface CaptureDetailsParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
}

const useCaptureDetails = () => {
  const [captureDetailsData, setCaptureDetailsData] = useState<CaptureDetailsResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (captureDetailsParams: CaptureDetailsParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setCaptureDetailsData(null);
      setError(null);

      const captureDetailsResponse = (await getCaptureDetails({
        params: captureDetailsParams,
        signal: controller.current.signal,
      })) as unknown as CaptureDetailsResponse;

      setCaptureDetailsData(captureDetailsResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, captureDetailsData, loading, error];
};

export { useCaptureDetails };
