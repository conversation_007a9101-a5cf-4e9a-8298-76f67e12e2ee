import { useState } from "react";
import { getMostScheduled, MostScheduledResponse } from "../../services/analyticsService";
import useAbortController from "../useAbortController";

export type MostScheduledOption = "floorplans" | "layouts";

interface MostScheduledParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
}

const useMostScheduled = () => {
  const [mostScheduledData, setMostScheduledData] = useState<MostScheduledResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (option: MostScheduledOption, mostScheduledParams: MostScheduledParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setMostScheduledData(null);
      setError(null);

      const mostScheduledResponse = (await getMostScheduled(option, {
        params: mostScheduledParams,
        signal: controller.current.signal,
      })) as unknown as MostScheduledResponse;

      setMostScheduledData(mostScheduledResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, mostScheduledData, loading, error];
};

export { useMostScheduled };
