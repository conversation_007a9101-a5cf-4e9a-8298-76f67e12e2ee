import { useState } from "react";
import { getMostViewed, MostViewedResponse } from "../../services/analyticsService";
import useAbortController from "../useAbortController";

export type MostViewedOption = "floorplans" | "layouts" | "amenities";

export type ProspectsType = "anonymous" | "identified";

interface MostScheduledParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
  prospects: ProspectsType;
}

const useMostViewed = () => {
  const [mostViewedData, setMostViewedData] = useState<MostViewedResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (option: MostViewedOption, mostViewedParams: MostScheduledParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setMostViewedData(null);
      setError(null);

      const mostViewedResponse = (await getMostViewed(option, {
        params: mostViewedParams,
        signal: controller.current.signal,
      })) as unknown as MostViewedResponse;

      setMostViewedData(mostViewedResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, mostViewedData, loading, error];
};

export { useMostViewed };
