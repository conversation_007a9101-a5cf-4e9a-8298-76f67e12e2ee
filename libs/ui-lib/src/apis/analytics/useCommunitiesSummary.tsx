import { getCommunitiesSummary } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";
import { ProspectsType } from "./useMostViewed";

interface CommunitiesSummaryParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
  prospects: ProspectsType;
}

const useCommunitiesSummary = () => {
  const [communitiesSummaryData, setCommunitiesSummaryData] = useState<any | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (communitiesSummaryParams: CommunitiesSummaryParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setCommunitiesSummaryData(null);
      setError(null);

      const communitiesSummaryResponse = (await getCommunitiesSummary({
        params: communitiesSummaryParams,
        signal: controller.current.signal,
      })) as unknown as any;

      setCommunitiesSummaryData(communitiesSummaryResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, communitiesSummaryData, loading, error];
};

export { useCommunitiesSummary };
