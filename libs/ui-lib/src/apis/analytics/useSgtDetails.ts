import { getSgtDetails, SgtPortfolioDetailsResponse } from "@services";
import { useEffect, useRef, useState } from "react";
import useAbortController from "../useAbortController";

interface SgtDetailsParams {
  communityIds: string[];
  startDate: string;
  endDate: string;
  groupBy: "community" | "space";
}

const useSgtDetails = () => {
  const [sgtDetails, setSgtDetails] = useState<any | null>(null!);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (sgtDetailsParams: SgtDetailsParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setSgtDetails(null);
      setError(null);

      const res = await getSgtDetails({
        params: sgtDetailsParams,
        signal: controller.current.signal,
      });

      setSgtDetails({ data: res as unknown as SgtPortfolioDetailsResponse });
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, sgtDetails, loading, error];
};

export { useSgtDetails };
