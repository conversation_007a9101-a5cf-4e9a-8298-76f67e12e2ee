import { getLeasingVelocity, LeasingVelocityResponse } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";

interface LeasingVelocityParams {
  communityIds: string[];
  period: {
    startDate: Date;
    endDate: Date;
  };
}

const useLeasingVelocity = () => {
  const [leasingVelocityData, setLeasingVelocityData] = useState<LeasingVelocityResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (leasingVelocityParams: LeasingVelocityParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setLeasingVelocityData(null);
      setError(null);

      const leasingVeolictyResponse = (await getLeasingVelocity({
        params: leasingVelocityParams,
        signal: controller.current.signal,
      })) as unknown as LeasingVelocityResponse;

      setLeasingVelocityData(leasingVeolictyResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, leasingVelocityData, loading, error];
};

export { useLeasingVelocity };
