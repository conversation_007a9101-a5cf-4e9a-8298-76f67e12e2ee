import { useState } from "react";
import { CaptureSummaryResponse, getCaptureSummary } from "../../services/analyticsService";
import useAbortController from "../useAbortController";

interface CaptureSummaryParams {
  communityIds: string[];
  startDate: Date;
  endDate: Date;
}

const useCaptureSummary = () => {
  const [captureSummaryData, setCaptureSummaryData] = useState<CaptureSummaryResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (captureDetailsParams: CaptureSummaryParams) => {
    abortIfNeeded();

    try {
      setLoading(true);
      setCaptureSummaryData(null);
      setError(null);

      const captureDetailsResponse = (await getCaptureSummary({
        params: captureDetailsParams,
        signal: controller.current.signal,
      })) as unknown as CaptureSummaryResponse;

      setCaptureSummaryData(captureDetailsResponse);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, captureSummaryData, loading, error];
};

export { useCaptureSummary };
