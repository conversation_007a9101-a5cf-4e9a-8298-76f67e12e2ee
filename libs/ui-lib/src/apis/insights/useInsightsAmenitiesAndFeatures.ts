import { useState } from "react";
import { getInsightsAmenitiesAndFeatures } from "@services";
import { parseParams } from "@utils";
import { InsightsAmenitiesAndFeatures, InsightsFilters } from "@types";
import useAbortController from "../useAbortController";

const useInsightsAmenitiesAndFeatures = () => {
  const [amenitiesAndFeatures, setAmenitiesAndFeatures] = useState<InsightsAmenitiesAndFeatures[]>(null!);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (params?: InsightsFilters, ids?: string[]) => {
    try {
      abortIfNeeded();
      setLoading(true);
      setAmenitiesAndFeatures(null!);
      setError(null);

      const parsedParams = parseParams(params);
      const response = (await getInsightsAmenitiesAndFeatures({
        params: {
          ids,
          ...parsedParams,
        },
        signal: controller.current.signal,
      })) as unknown as InsightsAmenitiesAndFeatures[];
      setAmenitiesAndFeatures(response);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, amenitiesAndFeatures, loading, error];
};

export { useInsightsAmenitiesAndFeatures };
