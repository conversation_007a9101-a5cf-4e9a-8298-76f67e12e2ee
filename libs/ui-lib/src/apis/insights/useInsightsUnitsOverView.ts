import { getUnitsInsightsOverview, UnitsInsightsOverview } from "@services";
import { InsightsFilters } from "@types";
import { filter } from "jszip";
import { useState } from "react";
import useAbortController from "../useAbortController";

const useInsightsUnitsOverview = () => {
  const [data, setData] = useState<UnitsInsightsOverview>(null!);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (insightsParams?: InsightsFilters) => {
    try {
      abortIfNeeded();
      setLoading(true);
      setData(null);
      setError(null);
      const data = (await getUnitsInsightsOverview({
        params: {
          ...insightsParams,
        },
        signal: controller.current.signal,
      })) as unknown as UnitsInsightsOverview;
      setData(data);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, data, loading, error, filter];
};

export { useInsightsUnitsOverview };
