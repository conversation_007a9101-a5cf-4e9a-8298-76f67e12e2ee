import { getInsightsPropertyLeases } from "@services";
import { useState } from "react";
import useAbortController from "../useAbortController";
import { InsightsPropertyLease } from "@types";

const useInsightsPropertiesLeases = () => {
  const [leasesOverview, setLeasesOverview] = useState<InsightsPropertyLease[] | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (ids?: string[], bedroom?: number, dateRange?: { startDate?: string; endDate?: string }) => {
    try {
      abortIfNeeded();
      setLoading(true);
      setLeasesOverview(null);
      setError(null);

      if (!ids || ids.length === 0) return;
      const params = {
        ids: ids || [],
        bedrooms: bedroom,
        dateRange: null,
      };

      if (dateRange?.startDate && dateRange?.endDate) {
        params.dateRange = dateRange;
      }

      const response = (await getInsightsPropertyLeases({
        params,
        signal: controller.current.signal,
      })) as unknown as InsightsPropertyLease[];
      setLeasesOverview(response);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, leasesOverview, loading, error] as const;
};

export { useInsightsPropertiesLeases };
