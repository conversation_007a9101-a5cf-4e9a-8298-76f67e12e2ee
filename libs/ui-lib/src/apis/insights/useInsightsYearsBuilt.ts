import { useState } from "react";
import { getInsightsYearsBuilt } from "@services";
import { InsightsYearBuilt } from "@types";

const useInsightsYearsBuilt = () => {
  const [yearsBuilt, setYearsBuilt] = useState<InsightsYearBuilt[]>([]);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const api = async () => {
    try {
      setLoading(true);
      setYearsBuilt([]);
      setError(null);
      const response = (await getInsightsYearsBuilt()) as unknown as InsightsYearBuilt[];
      setYearsBuilt(response);
      setLoading(false);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return [api, yearsBuilt, loading, error];
};

export { useInsightsYearsBuilt };
