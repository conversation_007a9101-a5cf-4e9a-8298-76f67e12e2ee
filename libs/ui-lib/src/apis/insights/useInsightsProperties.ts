import { InsightsPropertiesResponse, getInsightsProperties } from "@services";
import { InsightsFilters } from "@types";
import { useState } from "react";
import { parseParams } from "@utils";
import useAbortController from "../useAbortController";

const useInsightsProperties = () => {
  const [properties, setProperties] = useState<InsightsPropertiesResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (filters: InsightsFilters) => {
    try {
      abortIfNeeded();
      setLoading(true);
      setProperties(null);
      setError(null);
      const propertyParams = parseParams(filters);
      const response = (await getInsightsProperties({
        params: propertyParams,
        signal: controller.current.signal,
      })) as unknown as InsightsPropertiesResponse;
      setProperties(response);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, properties, loading, error];
};

export { useInsightsProperties };
