import { useState } from "react";
import { getInsightsPropertyAverages, InsightsPropertiesAveragesResponse } from "@services";
import { PropertiesAveragesFilters } from "@types";
import { parseParams } from "@utils";
import useAbortController from "../useAbortController";

const useInsightsPropertyAverages = () => {
  const [data, setData] = useState<InsightsPropertiesAveragesResponse | null>(null);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async (propertiesAveragesFilters?: PropertiesAveragesFilters) => {
    try {
      abortIfNeeded();
      setLoading(true);
      setData(null);
      setError(null);
      const parsedParams = parseParams(propertiesAveragesFilters);
      const response = (await getInsightsPropertyAverages({
        params: {
          ...parsedParams,
        },
        signal: controller.current.signal,
      })) as unknown as InsightsPropertiesAveragesResponse;

      console.log("response", response);
      setData(response);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, data, loading, error];
};

export { useInsightsPropertyAverages };
