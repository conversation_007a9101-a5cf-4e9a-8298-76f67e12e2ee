import { useState } from "react";
import { getInsightsBuildingTypes } from "@services";
import { InsightsBuildingTypes } from "@types";

const useInsightsBuildingTypes = () => {
  const [buildingTypes, setBuildingTypes] = useState<InsightsBuildingTypes[]>([]);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const api = async () => {
    try {
      setLoading(true);
      setBuildingTypes([]);
      setError(null);

      const response = (await getInsightsBuildingTypes()) as unknown as InsightsBuildingTypes[];
      setBuildingTypes(response);
      setLoading(false);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return [api, buildingTypes, loading, error];
};

export { useInsightsBuildingTypes };
