import { useState } from "react";
import { getInsightsUnitsAndFeatures } from "@services";
import { InsightsAmenitiesAndFeatures } from "@types";
import useAbortController from "../useAbortController";

const useInsightsUnitsAndFeatures = () => {
  const [unitsAndFeatures, setUnitsAndFeatures] = useState<InsightsAmenitiesAndFeatures[]>([]);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async () => {
    try {
      abortIfNeeded();
      setLoading(true);
      setUnitsAndFeatures([]);
      setError(null);

      const response = (await getInsightsUnitsAndFeatures({
        signal: controller.current.signal,
      })) as unknown as InsightsAmenitiesAndFeatures[];
      setUnitsAndFeatures(response);
      setLoading(false);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, unitsAndFeatures, loading, error];
};

export { useInsightsUnitsAndFeatures };
