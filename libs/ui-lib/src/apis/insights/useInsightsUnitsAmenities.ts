import { useState } from "react";
import { getInsightsUnitsAmenities } from "@services";
import { InsightsAmenities } from "@types";

const useInsightsUnitsAmenities = () => {
  const [amenities, setAmenities] = useState<InsightsAmenities[]>([]);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const api = async () => {
    try {
      setLoading(true);
      setAmenities([]);
      setError(null);
      const response = (await getInsightsUnitsAmenities()) as unknown as InsightsAmenities[];
      setAmenities(response);
      setLoading(false);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  return [api, amenities, loading, error];
};

export { useInsightsUnitsAmenities };
