import { getUnitsLevelInsights, UnitsLevelInsightsResponse } from "@services";
import { InsightsFilters } from "@types";
import { useState } from "react";
import useAbortController from "../useAbortController";
import { parseParams } from "@utils";

const useInsightsUnitsLevel = () => {
  const [data, setData] = useState<UnitsLevelInsightsResponse>(null!);
  const [error, setError] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { controller, abortIfNeeded } = useAbortController();

  const api = async ({ insightsParams, ids }: { insightsParams: InsightsFilters; ids: string[] }) => {
    try {
      abortIfNeeded();
      setLoading(true);
      setData(null);
      setError(null);
      const params = parseParams(insightsParams);
      const data = (await getUnitsLevelInsights({
        params: {
          ids,
          ...params,
        },
        signal: controller.current.signal,
      })) as unknown as UnitsLevelInsightsResponse;

      setData(data);
      setLoading(false);
    } catch (error) {
      if (error.message !== "canceled") {
        setError(error);
        setLoading(false);
      }
    }
  };

  return [api, data, loading, error];
};

export { useInsightsUnitsLevel };
