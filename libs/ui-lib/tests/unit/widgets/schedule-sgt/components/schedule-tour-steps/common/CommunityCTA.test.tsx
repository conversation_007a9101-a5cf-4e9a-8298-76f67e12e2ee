import CommunityCTA from "@widgets/schedule-sgt/components/schedule-tour-steps/common/CommunityCTA";
import React from "react";
import "@testing-library/jest-dom";
import { render, fireEvent } from "@testing-library/react";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";

// mock useScheduleTour
jest.mock("@widgets/components/schedule-tour-layout/ScheduleTourProvider", () => ({
  __esModule: true,
  useScheduleTour: jest.fn(() => ({
    community: {
      mapLink: "https://www.google.com/maps",
      guideLink: "https://www.google.com/guide",
    },
  })),
}));

// mock useAnalytics
jest.mock("@utils", () => ({
  __esModule: true,
  useAnalytics: jest.fn(() => ({
    track: jest.fn(),
  })),
}));

// mock window.open
const mockWindowOpen = jest.fn();
Object.defineProperty(window, "open", {
  value: mockWindowOpen,
});

describe("CommunityCTA", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockWindowOpen.mockClear();
  });

  it("renders correctly with both guidebook and map buttons", () => {
    const { getByText } = render(<CommunityCTA />);
    expect(getByText("Guidebook")).toBeInTheDocument();
    expect(getByText("Map")).toBeInTheDocument();
  });

  it("opens guidebook link when guidebook button is clicked", () => {
    const mockTrack = jest.fn();
    require("@utils").useAnalytics.mockReturnValue({ track: mockTrack });

    const { getByText } = render(<CommunityCTA />);
    const guidebookButton = getByText("Guidebook");

    fireEvent.click(guidebookButton);

    expect(mockTrack).toHaveBeenCalledWith("cta_guidebook");
    expect(mockWindowOpen).toHaveBeenCalledWith("https://www.google.com/guide", "_blank");
  });

  it("opens map link when map button is clicked", () => {
    const mockTrack = jest.fn();
    require("@utils").useAnalytics.mockReturnValue({ track: mockTrack });

    const { getByText } = render(<CommunityCTA />);
    const mapButton = getByText("Map");

    fireEvent.click(mapButton);

    expect(mockTrack).toHaveBeenCalledWith("cta_map");
    expect(mockWindowOpen).toHaveBeenCalledWith("https://www.google.com/maps", "_blank");
  });

  it("renders correctly when community doesn't have map link and guide link", () => {
    (useScheduleTour as jest.Mock).mockImplementation(() => ({
      community: {
        mapLink: null,
        guideLink: null,
      },
    }));

    const { getByText } = render(<CommunityCTA />);
    expect(getByText("Guidebook")).toBeInTheDocument();
    expect(getByText("Map")).toBeInTheDocument();

    // Should still render buttons but with null links
    fireEvent.click(getByText("Guidebook"));
    expect(mockWindowOpen).toHaveBeenCalledWith(null, "_blank");
  });
});
