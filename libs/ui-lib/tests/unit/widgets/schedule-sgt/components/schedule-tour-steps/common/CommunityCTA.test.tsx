import AITourGuide from "@widgets/schedule-sgt/components/schedule-tour-steps/common/AITourGuide";
import React, { ReactNode } from "react";
import "@testing-library/jest-dom";
import { render } from "@testing-library/react";
import { useScheduleTour } from "@widgets/components/schedule-tour-layout/ScheduleTourProvider";

// mock TabLeavingWarning
jest.mock("@widgets/components/tab-leaving-warning", () => ({
  __esModule: true,
  TabLeavingWarning: () => <div data-testid="tab-leaving-warning-dialog" />,
}));

// mock SwipeableEdgeDrawer
jest.mock("@widgets/components/swipeable-drawer/SwipeableEdgeDrawer", () => ({
  __esModule: true,
  default: ({ children }: { children: ReactNode }) => <div data-testid="swipeable-edge-drawer">{children}</div>,
}));

// mock useScheduleTour
jest.mock("@widgets/schedule-sgt/components/schedule-tour-layout/ScheduleTourProvider", () => ({
  __esModule: true,
  useScheduleTour: jest.fn(() => ({
    community: {
      mapLink: "https://www.google.com/maps",
      guideLink: "https://www.google.com/guide",
      sgtSupportPhoneNumber: "**********",
    },
  })),
}));

// mock useAnalytics
jest.mock("@utils", () => ({
  __esModule: true,
  useAnalytics: jest.fn(() => ({
    user: jest.fn(() => ({
      userId: "**********",
      anonymousId: "**********",
    })),
    track: jest.fn(),
    getState: jest.fn(() => ({
      context: {
        sessionId: "**********",
      },
    })),
  })),
}));

describe("TabLeavingWarning", () => {
  it("renders correctly", () => {
    const { queryAllByTestId } = render(<AITourGuide />);
    expect(queryAllByTestId("cta-menu-item")).toHaveLength(3);
  });

  it("renders correctly when hideCommunityGuide is true", () => {
    const { queryAllByTestId } = render(<AITourGuide />);
    expect(queryAllByTestId("cta-menu-item")).toHaveLength(2);
  });

  it("renders correctly when community don't have map link and guide link", () => {
    (useScheduleTour as jest.Mock).mockImplementation(() => ({
      community: {
        mapLink: "",
        guideLink: "",
        sgtSupportPhoneNumber: "**********",
      },
    }));
    const { queryAllByTestId } = render(<AITourGuide />);
    expect(queryAllByTestId("cta-menu-item")).toHaveLength(1);
  });
});
