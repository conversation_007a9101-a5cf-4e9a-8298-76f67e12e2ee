{"private": true, "name": "frontend-monorepo", "workspaces": ["apps-docs/*", "apps-web/*", "apps-embed/*", "libs/*", "tools/*"], "scripts": {"build": "turbo run build --concurrency 15", "build:virtual-tour": "turbo run build --filter virtual-tour...", "build:dashboard": "turbo run build --filter dashboard...", "build:insights-dashboard": "turbo run build --filter insights-dashboard...", "build:self-guided-tour": "turbo run build --filter self-guided-tour...", "build:sideloader": "turbo run build --filter sideloader...", "build:chrome-extension": "turbo run build --filter chrome-extension...", "build:storybook": "turbo run build --filter peek-storybook...", "build:casa-widget": "turbo run build --filter casa-widget...", "dev": "turbo run dev --concurrency 15", "dev:virtual-tour": "turbo run dev --filter virtual-tour...", "dev:chrome-extension": "turbo run dev --filter chrome-extension...", "dev:dashboard": "turbo run dev --filter dashboard...", "dev:self-guided-tour": "turbo run dev --filter self-guided-tour...", "dev:sideloader": "turbo run dev --filter sideloader...", "dev:insights-dashboard": "turbo run dev --filter insights-dashboard...", "dev:storybook": "turbo run dev --filter peek-storybook...", "test": "turbo run test --concurrency 15", "test:virtual-tour": "turbo run test --filter virtual-tour", "test:dashboard": "turbo run test --filter dashboard", "test:self-guided-tour": "turbo run test --filter self-guided-tour", "test:sideloader": "turbo run test --filter sideloader", "test:ui-lib": "turbo run test --filter @piiqtechnologies/ui-lib", "lint": "turbo run lint", "type:check": "turbo run type:check", "format": "prettier --write \"**/*.{ts,tsx,md,js,mjs}\"", "postinstall": "husky install", "update:env:common": "bash  ./infra/fetch-aws-parameters.sh \"common\" \".env\"", "update:env:all": "bash ./infra/update-env.sh", "update:aws:params": "bash ./infra/update-aws-params.sh", "deploy": "turbo run deploy", "update:env": "turbo run update:env", "synth": "cdk synth"}, "engines": {"npm": ">=7.0.0", "node": ">=20.0.0"}, "packageManager": "yarn@3.5.1", "devDependencies": {"@piiqtechnologies/peek-iac": "3.4.9", "@piiqtechnologies/tsconfig": "workspace:*", "dotenv-cli": "^7.3.0", "eslint": "^8.48.0", "husky": "^8.0.3", "lint-staged": "^15.5.0", "prettier": "^3.0.2", "turbo": "latest"}, "dependencies": {"aws-cdk-lib": "^2.175.1", "ts-node": "^10.9.2"}}