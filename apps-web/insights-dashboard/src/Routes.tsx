import { ERROR_CODES } from "@components/error";
import { PATHS } from "@constants/Paths";
import { AppLayout, AuthLayout, DashboardLayout, InsightsLayout, LogoBoxLayout } from "@layouts";
import { PeekScreenLoader } from "@piiqtechnologies/ui-components";
import React, { Suspense } from "react";
import { Route, Routes } from "react-router-dom";

const Login = React.lazy(() => import("@pages/login"));
const SSOLoginError = React.lazy(() => import("@pages/sso-login-error"));
const ForgotPassword = React.lazy(() => import("@pages/forgot-password"));
const ResetPassword = React.lazy(() => import("@pages/reset-password"));
const Error = React.lazy(() => import("@pages/error"));
const PropertiesInsights = React.lazy(() => import("@pages/properties-insights"));
const FeaturesAndAmenitiesInsights = React.lazy(() => import("@pages/features-and-amenities-insights"));
const UnitInsights = React.lazy(() => import("@pages/units-insights"));

const AppRoutes = () => {
  return (
    <Suspense fallback={<PeekScreenLoader layered />}>
      <Routes>
        <Route element={<AppLayout />}>
          <Route element={<LogoBoxLayout />}>
            <Route index element={<Login />} />
            <Route path={PATHS.FORGOT_PASSWORD} element={<ForgotPassword />} />
            <Route path={PATHS.RESET_PASSWORD} element={<ResetPassword />} />
            <Route path={PATHS.LOGIN_ERROR} element={<SSOLoginError />} />
            <Route path={PATHS.ERROR} element={<Error errorCode={ERROR_CODES.INTERNAL_ERROR} />} />
            <Route path="*" element={<Error errorCode={ERROR_CODES.PAGE_NOT_FOUND} />} />
          </Route>
          <Route element={<AuthLayout />}>
            <Route element={<DashboardLayout />}>
              <Route path={PATHS.HOME} element={<InsightsLayout />}>
                <Route index element={<PropertiesInsights />} />
                <Route path={PATHS.PRORPERTIES_INSIGHTS} element={<PropertiesInsights />} />
                <Route path={PATHS.UNIT_INSIGHTS} element={<UnitInsights />} />
                <Route path={PATHS.FEATURES_AND_AMENITIES_INSIGHTS} element={<FeaturesAndAmenitiesInsights />} />
                <Route
                  path={PATHS.FEATURES_AND_AMENITIES_INSIGHTS}
                  element={<Error errorCode={ERROR_CODES.PAGE_NOT_FOUND} />}
                />
              </Route>
            </Route>
          </Route>
        </Route>
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
