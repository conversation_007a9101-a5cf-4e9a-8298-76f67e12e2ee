import React from "react";
import "./index.css";
import "@piiqtechnologies/ui-lib/lib/index.css";
import { ThemeProvider } from "@piiqtechnologies/ui-components";
import { IntlProvider } from "react-intl";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import GlobalStyles from "@mui/material/GlobalStyles";
import { ErrorProvider, LoaderProvider, NotificationProvider } from "@piiqtechnologies/ui-lib";
import AppRoutes from "./Routes";
import theme from "./theme";
import AuthContextProvider from "@stores/AuthContext";

const initLocale = "en";

export const App = () => {
  return (
    <BrowserRouter>
      <ErrorProvider>
        <LoaderProvider>
          <NotificationProvider>
            <ThemeProvider theme={theme}>
              <GlobalStyles
                styles={{
                  "*::-webkit-scrollbar": {
                    backgroundColor: "transparent",
                    width: 6,
                  },
                  "*::-webkit-scrollbar-track": {
                    boxShadow: "none !important",
                    backgroundColor: "transparent",
                  },
                  "*::-webkit-scrollbar-thumb": {
                    backgroundColor: theme.palette.grey[200],
                    borderRadius: theme.spacing(10),
                  },
                }}
              />
              <IntlProvider locale={initLocale}>
                <AuthContextProvider>
                  <AppRoutes />
                </AuthContextProvider>
              </IntlProvider>
            </ThemeProvider>
          </NotificationProvider>
        </LoaderProvider>
      </ErrorProvider>
    </BrowserRouter>
  );
};
