import { STORAGE } from "@constants/Storage";
import { User } from "@piiqtechnologies/ui-lib";

export type UserContext = { user?: User; sso?: boolean; proxyUser?: User };

export const getUserContext = () => {
  let userContext = null;
  try {
    const userDetailsString = sessionStorage.getItem(STORAGE.USER_CONTEXT);
    userContext = (userDetailsString && JSON.parse(userDetailsString)) || null;
  } catch {
    console.log("Session Storage access issue!!!");
  }
  return userContext;
};

export const storeUserContext = (useContext: UserContext) => {
  try {
    sessionStorage.setItem(STORAGE.USER_CONTEXT, JSON.stringify(useContext));
  } catch {
    console.log("Session Storage access issue!!!");
  }
};
