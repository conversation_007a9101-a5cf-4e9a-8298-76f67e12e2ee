import React, { createContext, useContext, useEffect, useMemo, useState } from "react";
import {
  LoginResponse,
  PolicyContextProvider,
  setToken,
  useError,
  useUser,
  useLoader,
  User,
  useAnalytics,
} from "@piiqtechnologies/ui-lib";
import { useLocation, useNavigate } from "react-router-dom";
import { PATHS } from "@constants/Paths";
import { getUserContext, storeUserContext, UserContext } from "./utils";

type AuthContextType = {
  onLogout: () => void;
  user: User;
  sso: boolean;
  onLogin: (loggedInUser: LoginResponse, sso: boolean) => void;
  reloadCurrentUser: () => void;
};

export const AuthContext = createContext<AuthContextType>(null!);

const AuthContextProvider: React.FC = ({ children }) => {
  const [userContext, setUserContext] = useState<UserContext>(getUserContext());
  const [getUser, user, getUserInProgress, getUserError] = useUser();
  const [shouldNavigate, setShouldNavigate] = useState(false);

  const analytics = useAnalytics();
  const navigate = useNavigate();
  const { state: locationState } = useLocation();
  const { setError } = useError();
  const { setLoader } = useLoader();

  useEffect(() => {
    setLoader(getUserInProgress, "getUserInProgress");
    return () => setLoader(false, "getUserInProgress");
  }, [getUserInProgress]);

  useEffect(() => {
    if (getUserError) {
      setError(getUserError?.message);
    }
  }, [getUserError]);

  const onLogin = (loggedInUser: LoginResponse, sso: boolean) => {
    setShouldNavigate(true);
    const { token } = loggedInUser;
    getUser({ userId: "__current" });
    setToken(token);
    setUserContext((_userContext) => ({ ..._userContext, sso }));
  };

  useEffect(() => {
    if (userContext?.user?._id) {
      analytics.identify(userContext.user._id, {
        name: userContext.user.name,
        email: userContext.user.email,
      });
    }
  }, [userContext]);

  useEffect(() => {
    if (!user) return;

    setUserContext((_userContext) => {
      storeUserContext({ ..._userContext, user });
      return { ..._userContext, user };
    });

    let redirectPath = `${PATHS.HOME}`;

    if (locationState) {
      const { redirectTo } = locationState;
      redirectPath = `${redirectTo.pathname}${redirectTo.search}`;
    }

    shouldNavigate && navigate(redirectPath, { replace: true });
  }, [user]);

  const onLogout = () => {
    setToken(null);
    sessionStorage.clear();
    setUserContext(null);
  };

  const reloadCurrentUser = () => {
    setShouldNavigate(false);
    getUser({ userId: "__current" });
  };

  return (
    <AuthContext.Provider
      value={{
        user: userContext?.user,
        sso: userContext?.sso,
        onLogin,
        onLogout,
        reloadCurrentUser,
      }}
    >
      {/* TODO rework on policy */}
      <PolicyContextProvider allow={userContext?.user?.allow || []} deny={userContext?.user?.deny || []}>
        {children}
      </PolicyContextProvider>
    </AuthContext.Provider>
  );
};

export function useAuthContext() {
  return useContext(AuthContext);
}

export default AuthContextProvider;
