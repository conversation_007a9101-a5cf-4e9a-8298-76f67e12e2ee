import React from "react";
import { PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import { useIntl } from "react-intl";
import messages from "./messages.i18n";

export enum ERROR_CODES {
  INACTIVE = "user_inactive",
  PENDING_APPROVAL = "sso_pending_access",
  PAGE_NOT_FOUND = "page_not_fount",
  INTERNAL_ERROR = "internal_error",
  USER_FORBIDDEN = "user_forbidden",
  USER_NOT_FOUND = "user_not_found",
}

interface ErrorProps {
  errorCode?: ERROR_CODES;
}

const getErrorMessages = (errorCode: string) => {
  const intl = useIntl();
  let errorTitle = messages.loginInvalidUserErrorTitle;
  let errorMessage = messages.loginInvalidUserErrorMessage;
  switch (errorCode) {
    case ERROR_CODES.INACTIVE: {
      errorTitle = messages.loginInactiveUserErrorTitle;
      errorMessage = messages.contactAdmin;
      break;
    }
    case ERROR_CODES.PENDING_APPROVAL: {
      errorTitle = messages.loginPendingApprovalError;
      errorMessage = messages.contactAdmin;
      break;
    }
    case ERROR_CODES.PAGE_NOT_FOUND: {
      errorTitle = messages.pageNotFoundError;
      errorMessage = messages.contactAdmin;
      break;
    }
    case ERROR_CODES.INTERNAL_ERROR: {
      errorTitle = messages.internalServerError;
      errorMessage = messages.contactAdmin;
      break;
    }
    case ERROR_CODES.USER_FORBIDDEN: {
      errorTitle = messages.userForbidden;
      errorMessage = messages.contactAdmin;
      break;
    }
    case ERROR_CODES.USER_NOT_FOUND: {
      errorTitle = messages.userNotFound;
      errorMessage = messages.contactAdmin;
      break;
    }
  }
  return { errorTitle: intl.formatMessage(errorTitle), errorMessage: intl.formatMessage(errorMessage) };
};

export const Error: React.FC<ErrorProps> = ({ errorCode }) => {
  const { errorMessage, errorTitle } = getErrorMessages(errorCode);
  return (
    <PeekStack spacing={2.5}>
      {errorCode !== ERROR_CODES.INACTIVE && errorCode !== ERROR_CODES.PENDING_APPROVAL && (
        <img src="/images/sadparrot.gif" width={27} height={35} alt="Error" />
      )}
      <PeekStack spacing={1}>
        <PeekTypography variant="body1" fontWeight="fontWeightBold">
          {errorTitle}
        </PeekTypography>
        <PeekTypography variant="body1">{errorMessage}</PeekTypography>
      </PeekStack>
    </PeekStack>
  );
};

export default Error;
