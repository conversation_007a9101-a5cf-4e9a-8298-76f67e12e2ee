import { defineMessages } from "react-intl";

const messages = defineMessages({
  internalServerError: {
    id: "app.error.internalError.title.text",
    defaultMessage: "Something went wrong!",
  },
  pageNotFoundError: {
    id: "app.error.pageNotFound.title.text",
    defaultMessage: "Page Not Found!",
  },
  contactAdmin: {
    id: "app.error.contactAdmin.message.text",
    defaultMessage: "Contact your organization’s Peek <NAME_EMAIL> for additional help.",
  },
  loginPendingApprovalError: {
    id: "app.error.login.pendingApproval.title.text",
    defaultMessage: "Your account is being created and pending approval!",
  },
  loginInvalidUserErrorTitle: {
    id: "app.error.login.invalidUser.title.text",
    defaultMessage: "This does not appear to be a registered Peek User.",
  },
  loginInvalidUserErrorMessage: {
    id: "app.error.login.invalidUser.message.text",
    defaultMessage:
      "Try again using your work email or contact your organization’s Peek <NAME_EMAIL> to get set up.",
  },
  loginInactiveUserErrorTitle: {
    id: "app.error.login.inactiveUser.title.text",
    defaultMessage: "Your account have been suspended.",
  },
  loginInactiveUserErrorMessage: {
    id: "app.error.login.inactiveUser.message.text",
    defaultMessage: "Please contact your organization’s Peek administrator.",
  },
  userForbidden: {
    id: "app.error.login.userForbidden.title.text",
    defaultMessage: "Your account is not registered or approved for access to the application.",
  },
  userNotFound: {
    id: "app.error.login.userNotFound.title.text",
    defaultMessage: "Your account is not registered or approved for access to the application.",
  },
});

export default messages;
