import { Peek<PERSON>ox, PeekButton, PeekTypography } from "@piiqtechnologies/ui-components";
import { useLoader } from "@piiqtechnologies/ui-lib";
import { Google as GoogleIcon } from "@piiqtechnologies/svg-icons";
import React from "react";
import { useIntl } from "react-intl";
import messages from "./messages.i18n";

interface GoogleSSOProps {}

export const GoogleSSO: React.FC<GoogleSSOProps> = () => {
  const intl = useIntl();
  const { setLoader } = useLoader();

  const onSignIn = () => {
    setLoader(true);
    const url = `${process.env.PEEK_APP_API_URL}auth/google?origin=insights-dashboard`;
    window.location.href = url;
  };
  return (
    <PeekButton
      fullWidth
      onClick={onSignIn}
      variant="outlined"
      startIcon={
        <PeekBox px={0.3} py={0}>
          <GoogleIcon height={12} width={12} />
        </PeekBox>
      }
    >
      <PeekTypography variant="body1" textTransform="none">
        {intl.formatMessage(messages.signInWithGoogle)}
      </PeekTypography>
    </PeekButton>
  );
};

export default GoogleSSO;
