import React from "react";
import { PeekButton, PeekTypography } from "@piiqtechnologies/ui-components";
import { Microsoft as MicrosoftIcon } from "@piiqtechnologies/svg-icons";
import { useIntl } from "react-intl";
import { useLoader } from "@piiqtechnologies/ui-lib";
import messages from "./messages.i18n";

interface MicrosoftSSOProps {}

export const MicrosoftSSO: React.FC<MicrosoftSSOProps> = () => {
  const intl = useIntl();
  const { setLoader } = useLoader();

  const onSignIn = () => {
    setLoader(true);
    const url = `${process.env.PEEK_APP_API_URL}auth/microsoft?origin=insights-dashboard`;
    window.location.href = url;
  };
  return (
    <PeekButton
      variant="contained"
      fullWidth
      onClick={onSignIn}
      startIcon={<MicrosoftIcon height={15} width={15} />}
      sx={{
        bgcolor: "common.black",
        ":hover": {
          bgcolor: "common.black",
        },
      }}
    >
      <PeekTypography variant="body1" textTransform="none">
        {intl.formatMessage(messages.signInWithMicrosoft)}
      </PeekTypography>
    </PeekButton>
  );
};

export default MicrosoftSSO;
