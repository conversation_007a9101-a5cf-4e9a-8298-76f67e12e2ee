import React, { useEffect } from "react";
import { useForgotPassword, useLoader, useNotification } from "@piiqtechnologies/ui-lib";
import { Formik } from "formik";
import { PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import { useIntl } from "react-intl";
import {
  ForgotPasswordFormFields,
  ForgotPasswordFormInitialValues,
  ForgotPasswordFormValidationSchema,
} from "./ForgotPasswordFormConfig";
import ForgotPasswordForm from "@common-components/form";
import { Values } from "@common-components/form/FormConfig";
import messages from "./messages.i18n";

interface ForgotPasswordProps {
  onSuccess: () => void;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onSuccess }) => {
  const [requestResetPassword, resetPasswordRequested, passwordResetInProgress, passwordResetError] =
    useForgotPassword();
  const { setNotification } = useNotification();
  const { setLoader } = useLoader();
  const intl = useIntl();

  useEffect(() => {
    setLoader(passwordResetInProgress);
    return () => setLoader(false);
  }, [passwordResetInProgress]);

  useEffect(() => {
    if (resetPasswordRequested) {
      setNotification(intl.formatMessage(messages.requestResetPasswordSuccessful));
      onSuccess();
    }
  }, [resetPasswordRequested]);

  const handleRequestPasswordReset = (values: Values) => {
    requestResetPassword(values);
  };

  return (
    <Formik
      initialValues={ForgotPasswordFormInitialValues}
      validationSchema={ForgotPasswordFormValidationSchema}
      onSubmit={handleRequestPasswordReset}
    >
      <PeekStack spacing={1.5}>
        <PeekStack spacing={1}>
          <PeekTypography variant="body1" fontWeight="fontWeightBold">
            {intl.formatMessage(messages.forgotPasswordTitle)}
          </PeekTypography>
          <PeekTypography variant="body1">{intl.formatMessage(messages.forgotPasswordInstruction)}</PeekTypography>
        </PeekStack>
        <ForgotPasswordForm
          formSpacing={2.5}
          error={passwordResetError}
          formFields={ForgotPasswordFormFields}
          submitText={intl.formatMessage(messages.requestResetPassword)}
        />
      </PeekStack>
    </Formik>
  );
};

export default ForgotPassword;
