import { FormFieldType, Values } from "@common-components/form/FormConfig";
import * as yup from "yup";

export const ForgotPasswordFormValidationSchema = yup.object({
  email: yup.string().email("Invalid email").required("Email address is required"),
});

export const ForgotPasswordFormInitialValues: Values = {
  email: "",
};

export const ForgotPasswordFormFields: FormFieldType[] = [
  {
    id: "email",
    label: "Email Address",
    placeholder: "Enter your email address",
    required: true,
  },
];
