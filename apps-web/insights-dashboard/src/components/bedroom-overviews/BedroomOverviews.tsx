import { InsightsContext } from "@layouts";
import { PeekBox } from "@piiqtechnologies/ui-components";
import { InsightsProperty } from "@piiqtechnologies/ui-lib";
import React, { useMemo } from "react";
import { useOutletContext } from "react-router-dom";
import { LeaseOverviewTable } from "./lease-overview-table";
import { Period } from "@components/common/date-range-picker";

interface BedroomOverviewTablesProps {
  selectedProperties: InsightsProperty[];
  dateRange: Period;
}

const bedroomsDictionary = {
  "0": "STUDIO",
  "1": "1 BEDROOM",
  "2": "2 BEDROOM",
  "3": "3+ BEDROOM",
};

const reverseBedroomType = {
  STUDIO: "0",
  "1 BEDROOM": "1",
  "2 BEDROOM": "2",
  "3+ BEDROOM": "3",
};

const BEDROOM_TYPES = ["STUDIO", "1 BEDROOM", "2 BEDROOM", "3+ BEDROOM"];

export const BedroomOverviews: React.FC<BedroomOverviewTablesProps> = ({ selectedProperties, dateRange }) => {
  const { insightsFilters } = useOutletContext<InsightsContext>();

  const bedroomTypes = useMemo(() => {
    if (insightsFilters && insightsFilters.layout) {
      if (insightsFilters.layout.length > 0) {
        const filteredTypes = insightsFilters.layout.map((layout) => bedroomsDictionary[layout]);
        const sortedTypes = BEDROOM_TYPES.filter((type) => filteredTypes.includes(type));
        return sortedTypes;
      }
    }
    return BEDROOM_TYPES;
  }, [insightsFilters]);

  const getBedroomType = (type: string) => {
    return reverseBedroomType[type];
  };

  return (
    <PeekBox>
      {selectedProperties?.length > 0 &&
        bedroomTypes.map((bedroomType) => (
          <LeaseOverviewTable
            selectedProperties={selectedProperties}
            title={bedroomType}
            bedroom={getBedroomType(bedroomType)}
            dateRange={dateRange}
          />
        ))}
    </PeekBox>
  );
};

export default BedroomOverviews;
