import {
  PeekPaper,
  PeekStack,
  PeekTable,
  PeekTableBody,
  PeekTableCell,
  PeekTableContainer,
  PeekTableRow,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { InsightsProperty, useInsightsPropertiesLeases } from "@piiqtechnologies/ui-lib";
import React, { useEffect, useMemo } from "react";
import { Period } from "@common-components/date-range-picker";

// Utility function to format numbers with proper currency and comma formatting
const formatValue = (value: string | number, key: string): string => {
  if (!value || value === "-" || value === "") return "-";

  const numValue = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(numValue)) return "-";

  // Format based on the type of value
  if (key.includes("rent") && !key.includes("sf")) {
    // Currency formatting for rent values
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numValue);
  } else if (key.includes("rent_sf")) {
    // Currency per square foot formatting
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numValue);
  } else if (numValue >= 1000) {
    // Large numbers with commas
    return new Intl.NumberFormat("en-US").format(numValue);
  }

  return numValue.toString();
};

interface LeaseOverviewTableProps {
  selectedProperties: InsightsProperty[];
  bedroom?: number;
  title?: string;
  dateRange: Period;
}

interface LeaseOverViewTableState {
  label: string;
  values: string[];
}

const dictionary = {
  avgAskingRent: "Avg Rent",
  medianAskingRent: "Median Rent",
  avgAskingRentPSF: "Avg Rent/SF",
  medianAskingRentPSF: "Median Rent/SF",
  avgNER: "Avg NER",
  medianNER: "Median NER",
  avgNERPSF: "Avg NER/SF",
  medianNERPSF: "Median NER/SF",
};

export const LeaseOverviewTable: React.FC<LeaseOverviewTableProps> = ({
  selectedProperties,
  title,
  bedroom,
  dateRange,
}) => {
  const theme = useTheme();
  const [fetchLeasesOverview, leasesOverview, loading] = useInsightsPropertiesLeases();

  const initialLeaseData: LeaseOverViewTableState[] = Object.keys(dictionary).map((key) => ({
    label: dictionary[key as keyof typeof dictionary],
    values: ["", "", "", "", ""],
  }));

  useEffect(() => {
    fetchLeasesOverview(
      selectedProperties.map((item) => item.id),
      bedroom,
      dateRange as any,
    );
  }, [selectedProperties, dateRange, bedroom]);

  const leaseData = useMemo(() => {
    if (!leasesOverview) {
      return initialLeaseData;
    }

    const orderedLeases: LeaseOverViewTableState[] = [];

    Object.keys(dictionary).forEach((key) => {
      const values: string[] = [];

      for (let i = 0; i < 5; i++) {
        if (i < selectedProperties.length) {
          const item = selectedProperties[i];
          const lease = leasesOverview.find((lease: any) => lease.id === item.id);
          if (lease && lease[key] !== undefined && lease[key] !== null) {
            values.push(formatValue(lease[key], key));
          } else {
            values.push("-");
          }
        } else {
          values.push("");
        }
      }

      orderedLeases.push({
        label: dictionary[key as keyof typeof dictionary],
        values: values,
      });
    });

    return orderedLeases;
  }, [leasesOverview, selectedProperties]);

  console.log("Lease Data:", leaseData, loading);
  return (
    <PeekPaper sx={{ p: 3, mt: 2 }}>
      <PeekTypography variant="h6" sx={{ mb: 2, color: theme.palette.text.primary, fontWeight: 500 }}>
        {title ? title : "LEASE OVERVIEW"}
      </PeekTypography>

      <PeekTableContainer>
        <PeekTable sx={{ borderCollapse: "separate", borderSpacing: "8px 0" }}>
          <PeekTableBody>
            {leaseData.map((row) => (
              <PeekTableRow key={row.label}>
                <PeekTableCell
                  sx={{
                    border: "none",
                    py: 1.5,
                    px: 2,
                    backgroundColor: theme.palette.grey[50],
                    fontWeight: 500,
                    minWidth: "120px",
                    borderRadius: "0 !important",
                  }}
                >
                  <PeekTypography variant="body2" fontWeight={500} sx={{ fontSize: "0.75rem" }}>
                    {row.label}
                  </PeekTypography>
                </PeekTableCell>
                {row.values.map((value, cellIndex) => (
                  <PeekTableCell
                    key={cellIndex}
                    sx={{
                      border: "none",
                      py: 1.5,
                      px: 2,
                      backgroundColor: theme.palette.grey[100],
                      minWidth: "140px",
                      textAlign: "center",
                      borderRadius: "0 !important",
                    }}
                  >
                    {typeof value === "string" ? (
                      <PeekTypography variant="body2" sx={{ fontSize: "0.75rem" }}>
                        {value}
                      </PeekTypography>
                    ) : (
                      <PeekStack justifyContent="center" alignContent="center">
                        {value}
                      </PeekStack>
                    )}
                  </PeekTableCell>
                ))}
              </PeekTableRow>
            ))}
          </PeekTableBody>
        </PeekTable>
      </PeekTableContainer>
    </PeekPaper>
  );
};

export default LeaseOverviewTable;
