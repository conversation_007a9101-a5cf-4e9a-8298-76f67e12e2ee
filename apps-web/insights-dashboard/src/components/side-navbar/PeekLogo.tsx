import { styled } from "@mui/material/styles";
import { PeekStack } from "@piiqtechnologies/ui-components";
import { PeekIcon as PeekImageIcon, PeekText as PeekTextIcon } from "@piiqtechnologies/svg-icons";
import React from "react";

type PeekLogoProps = {
  expanded: boolean;
};

const PeekIconLogo = styled(PeekImageIcon)(({ theme }) => ({
  color: theme.palette.secondary.main,
  width: "2.13em",
  height: "2.25em",
}));

//@ts-ignore
const PeekTextLogo = styled(PeekTextIcon)(({ theme, expanded }) => ({
  color: theme.palette.secondary.main,
  width: "5em",
  height: "2em",
  opacity: expanded ? 1 : 0,
  transition: "opacity .2s ease-out",
  "-webkit-transition": "opacity .2s ease-out",
  "-moz-transition": "opacity .2s ease-out",
  "-o-transition": "opacity .2s ease-out",
}));

export function PeekLogo({ expanded }: PeekLogoProps) {
  return (
    <PeekStack direction="row" spacing={2}>
      <PeekIconLogo />
      {/* @ts-ignore */}
      <PeekTextLogo expanded={expanded} />
    </PeekStack>
  );
}
