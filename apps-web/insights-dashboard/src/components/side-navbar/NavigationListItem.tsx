import React, { useMemo } from "react";
import {
  PeekListItemIcon,
  PeekListItemButton,
  PeekListItemText,
  PeekTypography,
  PeekListItem,
  PeekStack,
  useTheme,
  PeekBox,
} from "@piiqtechnologies/ui-components";

interface NavigationListItemProps {
  expanded: boolean;
  active?: boolean;
  label: string;
  subLabel?: string;
  onClick?: () => void;
  Icon: React.FC<any>;
  color?: string;
}

export const NavigationListItem: React.FC<NavigationListItemProps> = ({
  expanded,
  active,
  onClick,
  label,
  subLabel,
  Icon,
  color,
}) => {
  const theme = useTheme();

  const textColor = useMemo(() => {
    if (active) {
      return color ?? "secondary.main";
    }
    return "primary.main";
  }, [active, color]);

  return (
    <PeekListItem disablePadding alignItems="center" onClick={onClick}>
      <PeekListItemButton
        disableRipple
        sx={{
          justifyContent: "initial",
          gap: 1.8,
          py: 0.5,
          px: 3,
          alignItems: "center",
        }}
      >
        <PeekListItemIcon sx={{ minWidth: 0 }}>
          <PeekTypography
            variant="body2"
            color={(active && color) || textColor}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {active && (
              <PeekBox
                borderRadius={2}
                sx={{
                  position: "absolute",
                  width: theme.spacing(2.8),
                  height: theme.spacing(2.8),
                  borderRadius: theme.spacing(0.6),
                  backgroundColor: color || "secondary.main",
                  opacity: 0.1,
                }}
              />
            )}
            <Icon />
          </PeekTypography>
        </PeekListItemIcon>
        <PeekStack direction="row">
          <PeekListItemText
            primary={label}
            sx={{
              opacity: expanded ? "1" : "0",
              transition: "opacity .2s ease-out",
              "-webkit-transition": "opacity .2s ease-out",
              "-moz-transition": "opacity .2s ease-out",
              "-o-transition": "opacity .2s ease-out",
            }}
            primaryTypographyProps={{
              variant: "h3",
              fontWeight: "fontWeightRegular",
              color: (active && color) || textColor,
            }}
          />
          {subLabel && (
            <PeekTypography
              color={textColor}
              fontWeight="fontWeightMedium"
              fontSize="smaller"
              sx={{ verticalAlign: "sup" }}
            >
              <sup>{subLabel}</sup>
            </PeekTypography>
          )}
        </PeekStack>
      </PeekListItemButton>
    </PeekListItem>
  );
};

export default NavigationListItem;
