import React from "react";
import { MessageDescriptor } from "react-intl";
import messages from "./messages.i18n";
import { PATHS } from "@constants/Paths";

import { HomeV2 } from "@piiqtechnologies/svg-icons";

interface NavigationListItemConfig {
  label: MessageDescriptor;
  subLabel?: MessageDescriptor;
  path: PATHS;
  icon: React.FC<any>;
  iconSize?: number;
  color?: string;
}

export const NavigationListItemsConfig = (): NavigationListItemConfig[] => {
  const mainItems: NavigationListItemConfig[] = [
    {
      label: messages.home,
      path: PATHS.HOME,
      icon: HomeV2,
    },
  ];

  return mainItems;
};
