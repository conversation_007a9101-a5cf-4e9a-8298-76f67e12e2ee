import NavigationListItem from "@components/side-navbar/NavigationListItem";
import { PATHS } from "@constants/Paths";
import { Settings as SettingsIcon } from "@piiqtechnologies/svg-icons";
import React from "react";
import { useIntl } from "react-intl";
import { NavLink } from "react-router-dom";
import messages from "./messages.i18n";

interface SettingsProps {
  expanded: boolean;
}

export const Settings: React.FC<SettingsProps> = ({ expanded }) => {
  const intl = useIntl();

  return (
    <NavLink to={`/yet-to-decide`} style={{ textDecoration: "none" }}>
      {({ isActive }) => (
        <NavigationListItem
          Icon={() => <SettingsIcon width="20px" height="20px" />}
          label={intl.formatMessage(messages.settings)}
          expanded={expanded}
          active={isActive}
        />
      )}
    </NavLink>
  );
};

export default Settings;
