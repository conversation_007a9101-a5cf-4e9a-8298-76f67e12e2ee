import React from "react";
import { PATHS } from "@constants/Paths";
import { useNavigate } from "react-router-dom";
import { LogOutV2 as LogoutIcon } from "@piiqtechnologies/svg-icons";
import { useIntl } from "react-intl";
import messages from "./messages.i18n";
import NavigationListItem from "./NavigationListItem";
import { useAuthContext } from "@stores/AuthContext";
import { useAnalytics } from "@piiqtechnologies/ui-lib";

interface LogoutProps {
  expanded: boolean;
}

export const Logout: React.FC<LogoutProps> = ({ expanded }) => {
  const navigate = useNavigate();
  const intl = useIntl();
  const { onLogout } = useAuthContext();
  const analytics = useAnalytics();

  const doLogout = () => {
    sessionStorage.clear();
    onLogout();
    analytics.track("Logout");
    navigate(PATHS.LOGIN, { replace: true });
  };

  return (
    <NavigationListItem
      Icon={() => <LogoutIcon width="2em" height="2em" />}
      active={false}
      label={intl.formatMessage(messages.signOut)}
      expanded={expanded}
      onClick={doLogout}
    />
  );
};

export default Logout;
