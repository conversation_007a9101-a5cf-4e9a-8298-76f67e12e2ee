import { styled } from "@mui/material/styles";
import { <PERSON>eek<PERSON><PERSON>, PeekMiniDrawer, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import React, { useState } from "react";
import { useIntl } from "react-intl";
import { NavLink } from "react-router-dom";
import Logout from "./Logout";
import { NavigationListItemsConfig } from "./NavigationConfig";
import NavigationListItem from "./NavigationListItem";
import Settings from "./Settings";
import { PeekLogo } from "./PeekLogo";

const NavigationList = styled(PeekList)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(4),
}));

const SideBarHeader: React.FC = ({ children }) => (
  <PeekStack direction="row" justifyContent="flex-start">
    <PeekTypography>{children}</PeekTypography>
  </PeekStack>
);

interface SideNavBarProps {}

export const SideNavBar: React.FC<SideNavBarProps> = () => {
  const intl = useIntl();
  const [expanded, setExpanded] = useState(false);

  return (
    <PeekMiniDrawer variant="permanent" open={expanded} miniDrawerWidth={80} drawerWidth={258} bgColor="white">
      <PeekStack
        justifyContent="space-between"
        height="100%"
        onMouseOver={() => setExpanded(true)}
        onMouseLeave={() => setExpanded(false)}
        py={5.1}
        spacing={4}
      >
        <PeekStack spacing={1.5}>
          <PeekStack px={3} pb={2}>
            <SideBarHeader>
              <PeekStack gap={1}>
                <PeekLogo expanded={expanded} />
              </PeekStack>
            </SideBarHeader>
          </PeekStack>
          <NavigationList key="top-nav-items" disablePadding sx={{ gap: { md: 2, lg: 3 } }}>
            {NavigationListItemsConfig().map(({ label, subLabel, path, icon: Icon, iconSize, color }) => (
              <NavLink to={path} style={{ textDecoration: "none" }}>
                {({ isActive }) => (
                  <NavigationListItem
                    Icon={() => <Icon width={`${iconSize || 20}px`} height={`${iconSize || 20}px`} />}
                    label={intl.formatMessage(label)}
                    subLabel={subLabel && intl.formatMessage(subLabel)}
                    expanded={expanded}
                    active={isActive}
                    color={color}
                  />
                )}
              </NavLink>
            ))}
          </NavigationList>
        </PeekStack>
        <PeekStack spacing={{ md: 2, lg: 3 }}>
          <NavigationList key="bottom-nav-items-settings" disablePadding>
            <Settings expanded={expanded} />
          </NavigationList>
          <NavigationList key="bottom-nav-items" disablePadding>
            <Logout expanded={expanded} />
          </NavigationList>
        </PeekStack>
      </PeekStack>
    </PeekMiniDrawer>
  );
};

export default SideNavBar;
