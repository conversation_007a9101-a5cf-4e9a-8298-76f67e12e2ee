import React from "react";
import { PeekGrid, PeekTypography } from "@piiqtechnologies/ui-components";

interface FeaturesTooltipProps {
  features: string[];
}

const FeaturesTooltip = ({ features }: FeaturesTooltipProps) => {
  return (
    <PeekGrid container wrap="wrap" direction="row" spacing={1} justifyContent="flex-start">
      {features.map((amenity) => (
        <PeekGrid item key={amenity}>
          <PeekTypography variant="body1">&#8226; {amenity}</PeekTypography>
        </PeekGrid>
      ))}
    </PeekGrid>
  );
};

export default FeaturesTooltip;
