import NoDataFound from "@components/common/no-data-found";
import { Table } from "@components/common/table/Table";
import { TableApi } from "@components/common/table/types";
import { InsightsContext } from "@layouts";
import { InsightsUnit, useInsightsUnitsLevel } from "@piiqtechnologies/ui-lib";
import React from "react";
import { useOutletContext } from "react-router-dom";
import {
  DecoratedInsightsUnit,
  filterPredicate,
  sortablePredicate,
  UnitInsightsTableConfig,
} from "./UnitInsigtsTableConfig";

interface UnitInsightsTableProps {}

export const UnitInsightsTable: React.FC<UnitInsightsTableProps> = () => {
  const { selectedProperties } = useOutletContext<InsightsContext>();

  const unitsInsightsApi = useInsightsUnitsLevel() as TableApi<InsightsUnit>;

  const { unitInsightsColumns, unitInsightsParams, unitInsightsRowTransform } = UnitInsightsTableConfig({
    selectedProperties,
    data: unitsInsightsApi[1]?.data,
  });

  if (!selectedProperties || selectedProperties.length === 0) {
    return <NoDataFound message="Please select at least one property to view unit insights." />;
  }

  return (
    <Table<DecoratedInsightsUnit>
      api={unitsInsightsApi as TableApi<DecoratedInsightsUnit>}
      apiConfig={{
        params: unitInsightsParams,
        serverSidePagination: false,
        serverSideSorting: false,
        rowsPerPage: 2000,
      }}
      rowTransform={unitInsightsRowTransform}
      columns={unitInsightsColumns}
      notFoundMessage="No units data found."
      sortPredicate={sortablePredicate}
      filterPredicate={filterPredicate}
    />
  );
};

export default UnitInsightsTable;
