import { PeekBox, Peek<PERSON>hip, PeekTooltip } from "@piiqtechnologies/ui-components";
import React from "react";
import FeaturesTooltip from "./FeaturesTooltip";

interface FeaturesChipProps {
  features: string[];
}

const FeaturesChip: React.FC<FeaturesChipProps> = ({ features }) => {
  return (
    <PeekTooltip title={<FeaturesTooltip features={features} />} disableHoverListener={!features?.length}>
      <PeekBox>
        <PeekChip
          variant="filled"
          label={`${features?.length || "0"} in unit`}
          sx={{
            "&.MuiChip-filledDefault": { backgroundColor: features?.length ? "primary.contrastText" : "info.light" },
          }}
        />
      </PeekBox>
    </PeekTooltip>
  );
};

export default FeaturesChip;
