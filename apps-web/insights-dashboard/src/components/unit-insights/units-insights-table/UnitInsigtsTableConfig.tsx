import { TableColumnDefinition, TableState } from "@components/common/table/types";
import VirtualTourModal from "@components/common/virtual-tour-modal";
import { PlayCircle } from "@piiqtechnologies/svg-icons";
import { PeekIconButton, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import { InsightsProperty, InsightsUnit, useAnalytics } from "@piiqtechnologies/ui-lib";
import { format } from "date-fns";
import dot from "dot-object";
import React, { useCallback, useMemo } from "react";
import { useIntl } from "react-intl";
import FeaturesChip from "./FeaturesChip";

type UnitInsightsTableConfigProps = {
  selectedProperties: InsightsProperty[];
  data: InsightsUnit[];
};

export type DecoratedInsightsUnit = InsightsUnit & { _id: string };

const formatCurrency = (value: string | number): string => {
  if (!value) return null;
  const numValue = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(numValue)) return null;

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numValue);
};

const formatNumber = (value: string | number): string => {
  if (!value) return null;
  const numValue = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(numValue)) return null;

  return new Intl.NumberFormat("en-US").format(numValue);
};

const formatPSF = (value: string | number): string => {
  if (!value) return null;
  const numValue = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(numValue)) return null;

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numValue);
};

function calculatePSF(price, squareFeet) {
  // add parseflot to handle string inputs function and all other number validations
  const parsedPrice = parseFloat(price);
  const parsedSquareFeet = parseFloat(squareFeet);
  if (isNaN(parsedPrice) || isNaN(parsedSquareFeet) || parsedSquareFeet <= 0) {
    return null;
  }

  const psf = parsedPrice / parsedSquareFeet;
  return +psf.toFixed(2); // Return as a string with 2 decimal places
}

export const sortablePredicate = (a: InsightsUnit, b: InsightsUnit, state: TableState) => {
  const isDeep = state.orderBy.split(".").length > 1;
  //@ts-ignore
  const aValue = isDeep ? dot.pick(state.orderBy, a) : a[state.orderBy];
  //@ts-ignore
  const bValue = isDeep ? dot.pick(state.orderBy, b) : b[state.orderBy];

  if (aValue > bValue) return state.order === "asc" ? 1 : -1;
  if (aValue < bValue) return state.order === "asc" ? -1 : 1;
  if (aValue === bValue) return 0;
};

export const filterPredicate = (row: DecoratedInsightsUnit, filters: Record<string, Array<string>>) => {
  if (!filters || Object.keys(filters).length === 0) return true;

  for (const [key, values] of Object.entries(filters)) {
    if (!values.length) continue; // Skip empty filters

    const rowValue = row[key];
    // for tourUrl ans lastAvailable, we need to check if the row has a value
    if (key === "tourUrl" || key === "lastAvailable") {
      if (!rowValue) return false; // If the row does not have a value, it does not match the filter
      continue; // Skip further checks for these keys
    }

    if (key === "sqFt" || key === "rentAmount" || key === "psf") {
      // Handle range filtering for square footage
      let [min, max] = values.map((v) => parseFloat(v));
       if(isNaN(min))
        min = 0; // Default to 0 if min is NaN
      if(isNaN(max))
        max = Infinity; // Default to Infinity if max is NaN
      if (key === "psf") {
        const psf = calculatePSF(row.rentAmount, row.sqFt);
        if (psf < min || psf > max) return false; // Check if the price per square foot is within the range
      } else {
        if (rowValue < min || rowValue > max) return false; // Check if the row value is within the range
      }
      continue; // Skip further checks for square footage
    }

    if (Array.isArray(rowValue)) {
      // Handle array values
      if (!values.some((value) => rowValue.includes(value))) return false;
    } else {
      // Handle single value
      if (!values.includes(rowValue)) return false;
    }
  }
  return true;
};

export function UnitInsightsTableConfig({ selectedProperties, data }: UnitInsightsTableConfigProps) {
  const intl = useIntl();
  const analytics = useAnalytics();

  const unitInsightsRowTransform = useCallback((row: InsightsUnit): DecoratedInsightsUnit => {
    const decoratedRow: DecoratedInsightsUnit = {
      ...row,
      _id: row.id,
    };
    return decoratedRow;
  }, []);

  const unitInsightsParams = useMemo(() => {
    const ids = selectedProperties?.map((property) => property.id);
    return {
      ids,
    };
  }, [selectedProperties]);

  const unitInsightsColumns: TableColumnDefinition<DecoratedInsightsUnit>[] = useMemo(() => {
    return [
      {
        id: "communityName",
        label: "Community",
        sortable: true,
        accessor: "communityName",
        // unique community from the data
        filterOptions:
          Array.from(new Set(data?.map((unit) => unit.communityName)))
            ?.filter(Boolean)
            .map((communityName) => ({
              label: communityName,
              id: communityName,
            })) || [],
      },
      {
        id: "unit",
        label: "Unit #",
        sortable: true,
        accessor: "unit",
      },
      {
        id: "tourUrl",
        label: "Tour",
        sortable: false,
        render: (row) => {
          if (!row.tourUrl) return <PeekTypography></PeekTypography>;
          const [virtualTourModal, setVirtualTourModal] = React.useState({
            open: false,
            url: "",
            title: "",
          });
          return (
            <>
              <PeekStack direction="row" justifyContent="start" alignItems="center" justifyItems="center">
                <PeekIconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    analytics?.track("unit_virtual_tour_clicked", {
                      communityId: row.communityName,
                      unit: row.unit,
                      url: row.tourUrl,
                    });
                    setVirtualTourModal({
                      open: true,
                      url: row.tourUrl,
                      title: `${row.communityName} - Unit ${row.unit}`,
                    });
                  }}
                  bordered={false}
                  title={"View Virtual Tour"}
                >
                  <PeekTypography color={"secondary"} lineHeight={0}>
                    <PlayCircle height={14} width={14} />
                  </PeekTypography>
                </PeekIconButton>
              </PeekStack>
              <VirtualTourModal
                open={virtualTourModal.open}
                onClose={() => setVirtualTourModal({ open: false, url: "", title: "" })}
                virtualTourUrl={virtualTourModal.url}
                title={virtualTourModal.title}
              />
            </>
          );
        },
        filterOptions: [
          {
            id: "hasTour",
            label: "Has Tour",
          },
        ],
      },
      {
        id: "layout",
        label: "Layout",
        sortable: true,
        accessor: "layout",
        filterOptions:
          Array.from(new Set(data?.map((unit) => unit.layout)))
            ?.filter(Boolean)
            .map((layout) => ({
              label: layout,
              id: layout,
            })) || [],
      },
      {
        id: "floorplan",
        label: "Floorplan",
        sortable: true,
        accessor: "floorplan",
        filterOptions:
          Array.from(new Set(data?.map((unit) => unit.floorplan)))
            ?.filter(Boolean)
            .map((floorplan) => ({
              label: floorplan,
              id: floorplan,
            })) || [],
      },
      {
        id: "features",
        label: "Features",
        sortable: false,
        render: (row) => {
          const features = row.features;
          if (!features) return "-";
          return <FeaturesChip features={features.filter((f) => !!f)} />;
        },
        filterOptions:
          Array.from(new Set(data?.flatMap((unit) => unit.features ?? [])))
            ?.filter(Boolean)
            .map((feature) => ({
              label: feature,
              id: feature,
            })) || [],
      },
      {
        id: "sqFt",
        label: "SF",
        sortable: true,
        render: (row) => {
          return formatNumber(row.sqFt);
        },
        filterOptions:
          Array.from(new Set(data?.map((unit) => unit.sqFt)))
            ?.filter(Boolean)
            .map((sqFt) => ({
              label: formatNumber(sqFt),
              id: sqFt,
            })) || [],
        rangeFilter: true, // Enable range filtering for square footage
      },
      {
        id: "psf",
        label: "PSF",
        sortable: false,
        render: (row) => {
          const psf = calculatePSF(row.rentAmount, row.sqFt);
          return formatPSF(psf);
        },
        filterOptions:
          Array.from(new Set(data?.map((unit) => calculatePSF(unit.rentAmount, unit.sqFt))))
            ?.filter(Boolean)
            .map((psf) => ({
              label: formatPSF(psf),
              id: psf,
            })) || [],
        rangeFilter: true, // Enable range filtering for price per square foot
      },
      {
        id: "rentAmount",
        label: "Rent (12m)",
        sortable: true,
        render: (row) => {
          return formatCurrency(row.rentAmount);
        },
        filterOptions:
          Array.from(new Set(data?.map((unit) => unit.rentAmount)))
            ?.filter(Boolean)
            .map((rentAmount) => ({
              label: formatCurrency(rentAmount),
              id: rentAmount,
            })) || [],
        rangeFilter: true, // Enable range filtering for rent amount
      },
      {
        id: "lastAvailable",
        label: "Available",
        sortable: true,
        render: (row) => {
          if (!row.lastAvailable) return "-";
          return format(new Date(row.lastAvailable), "MM/dd/yyyy");
        },
        filterOptions: [
          {
            id: "hasAvailable",
            label: "Available",
          },
        ],
      },
    ] as TableColumnDefinition<DecoratedInsightsUnit>[];
  }, [data]);

  return {
    unitInsightsParams,
    unitInsightsColumns,
    unitInsightsRowTransform,
  };
}
