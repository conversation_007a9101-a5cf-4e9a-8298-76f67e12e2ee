import React, { useEffect } from "react";
import {
  PeekCircularProgress,
  PeekDivider,
  PeekPaper,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { useOutletContext } from "react-router-dom";
import { useInsightsUnitsOverview } from "@piiqtechnologies/ui-lib";
import { InsightsContext } from "@layouts";

export const UnitInsightsOverview = () => {
  const theme = useTheme();
  const { insightsFilters } = useOutletContext<InsightsContext>();

  const [api, data, loading, error] = useInsightsUnitsOverview();

  useEffect(() => {
    api(insightsFilters);
  }, [insightsFilters]);

  return (
    <PeekStack
      direction="row"
      spacing={theme.spacing(0.5)}
      width={theme.spacing(10)}
      sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%" }}
    >
      {loading && !error && (
        <PeekStack height="100%" width="100%" alignItems="center" justifyContent="center" padding={theme.spacing(3)}>
          <PeekCircularProgress color="secondary" />
        </PeekStack>
      )}
      {error && !loading && (
        <PeekStack height="100%" width="100%" alignItems="center" justifyContent="center" padding={theme.spacing(3)}>
          <PeekTypography variant="h4" color="error">
            Could not load data
          </PeekTypography>
        </PeekStack>
      )}
      {!loading &&
        !error &&
        data &&
        data.map((item, index) => (
          <PeekPaper key={index} sx={{ minWidth: theme.spacing(20) }}>
            <PeekStack direction="column" spacing={1.5} sx={{ padding: theme.spacing(1.5), alignItems: "center" }}>
              <PeekTypography variant="h4" color="grey.400" fontWeight="fontWeightMedium">
                {item.title}
              </PeekTypography>
            </PeekStack>
            <PeekDivider light orientation="horizontal" />
            <PeekStack direction="row" sx={{ justifyContent: "space-between" }}>
              <PeekStack
                direction="column"
                spacing={0.5}
                sx={{ alignItems: "center", width: "100%", padding: theme.spacing(2) }}
              >
                <PeekTypography variant="h2" fontWeight="fontWeightMedium">
                  {item.available}
                </PeekTypography>
                <PeekTypography variant="h4" color="grey.400">
                  Available
                </PeekTypography>
              </PeekStack>
              <PeekStack
                direction="column"
                spacing={0.5}
                sx={{ alignItems: "center", width: "100%", padding: theme.spacing(2) }}
              >
                <PeekTypography variant="h2" fontWeight="fontWeightMedium">
                  {item.leased}
                </PeekTypography>
                <PeekTypography variant="h4" color="grey.400">
                  Leased
                </PeekTypography>
              </PeekStack>
            </PeekStack>
          </PeekPaper>
        ))}
    </PeekStack>
  );
};

export default UnitInsightsOverview;
