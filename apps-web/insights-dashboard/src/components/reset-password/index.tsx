import React, { useEffect } from "react";
import { useResetPassword, useLoader, useNotification } from "@piiqtechnologies/ui-lib";
import { Formik } from "formik";
import { useIntl } from "react-intl";
import {
  PASSWORD_REQUIREMENT_MESSAGE,
  ResetPasswordFormFields,
  ResetPasswordFormInitialValues,
  ResetPasswordFormValidationSchema,
} from "./ResetPasswordFormConfig";
import ResetPasswordForm from "@common-components/form";
import { PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import { Values } from "@common-components/form/FormConfig";
import messages from "./messages.i18n";

interface ResetPasswordProps {
  onResetPasswordSuccess: () => void;
  resetToken: string;
}

const ResetPassword: React.FC<ResetPasswordProps> = ({ onResetPasswordSuccess, resetToken }) => {
  const [resetPassword, loggedInUser, resetPasswordInProgress, resetPasswordError] = useResetPassword();
  const { setLoader } = useLoader();
  const { setNotification } = useNotification();
  const intl = useIntl();

  useEffect(() => {
    setLoader(resetPasswordInProgress);
    return () => setLoader(false);
  }, [resetPasswordInProgress]);

  useEffect(() => {
    if (loggedInUser) {
      setNotification(intl.formatMessage(messages.resetPasswordSuccess));
      onResetPasswordSuccess();
    }
  }, [loggedInUser]);

  const handleResetPassword = ({ password }: Values) => {
    resetPassword({ password, token: resetToken });
  };

  return (
    <Formik
      initialValues={ResetPasswordFormInitialValues}
      initialTouched={{ password: true as boolean }}
      initialErrors={{ password: PASSWORD_REQUIREMENT_MESSAGE as string }}
      validationSchema={ResetPasswordFormValidationSchema}
      onSubmit={handleResetPassword}
    >
      <PeekStack spacing={1.5}>
        <PeekTypography variant="body1" fontWeight="fontWeightBold">
          {intl.formatMessage(messages.resetPasswordFormTitle)}
        </PeekTypography>
        <ResetPasswordForm
          formSpacing={2}
          error={resetPasswordError}
          formFields={ResetPasswordFormFields}
          submitText={intl.formatMessage(messages.resetPassword)}
        />
      </PeekStack>
    </Formik>
  );
};

export default ResetPassword;
