import { Values, FormFieldType } from "@common-components/form/FormConfig";
import * as yup from "yup";

export const PASSWORD_REQUIREMENT_MESSAGE =
  "Password must have at least 8 characters, 1 uppercase, 1 lowercase, and 1 number and 1 special character.";

export const ResetPasswordFormValidationSchema = yup.object({
  password: yup
    .string()
    .required(PASSWORD_REQUIREMENT_MESSAGE)
    .matches(/^(?=.*\d)(?=.*[!@#$%^&*])(?=.*[a-z])(?=.*[A-Z]).{8,}$/, PASSWORD_REQUIREMENT_MESSAGE),
  confirmPassword: yup
    .string()
    .required("Please retype your new password.")
    .oneOf([yup.ref("password")], "Password didn’t match. Please try again."),
});

export const ResetPasswordFormInitialValues: Values = {
  password: "",
  confirmPassword: "",
};

export const ResetPasswordFormFields: FormFieldType[] = [
  {
    id: "password",
    label: "Create New Password",
    placeholder: "Enter your new password",
    required: true,
    type: "password",
  },
  {
    id: "confirmPassword",
    label: "Confirm Password",
    placeholder: "Retype your new password",
    required: true,
    type: "password",
  },
];
