import React, { useEffect } from "react";
import { useLogin, useLoader, LoginResponse } from "@piiqtechnologies/ui-lib";
import { Formik } from "formik";
import { useIntl } from "react-intl";
import { LoginFormFields, LoginFormInitialValues, LoginFormValidationSchema } from "./LoginFormConfig";
import LoginForm from "@common-components/form";
import { Values } from "@common-components/form/FormConfig";
import messages from "./messages.i18n";
import { useNavigate, createSearchParams } from "react-router-dom";
import { PATHS } from "@constants/Paths";
interface LoginProps {
  onLoginSuccess: (loggedInUser: LoginResponse) => void;
}

const Login: React.FC<LoginProps> = ({ onLoginSuccess }) => {
  const [doLogin, loggedInUser, loginProgress, loginError] = useLogin();
  const intl = useIntl();
  const navigate = useNavigate();
  const { setLoader } = useLoader();

  useEffect(() => {
    setLoader(loginProgress);
    return () => setLoader(false);
  }, [loginProgress]);

  useEffect(() => {
    if (loggedInUser) {
      onLoginSuccess(loggedInUser);
    }
  }, [loggedInUser]);

  const handleLogin = (values: Values) => {
    doLogin({
      ...values,
      origin: "insights-dashboard",
    });
  };

  useEffect(() => {
    if (loginError?.cause?.code === 10009) {
      navigate({
        pathname: PATHS.LOGIN_ERROR,
        search: createSearchParams({
          message: "user_inactive",
        }).toString(),
      });
    }

    if (loginError?.cause?.code === 403) {
      navigate({
        pathname: PATHS.LOGIN_ERROR,
        search: createSearchParams({
          message: "user_forbidden",
        }).toString(),
      });
    }
  }, [loginError]);

  return (
    <Formik initialValues={LoginFormInitialValues} validationSchema={LoginFormValidationSchema} onSubmit={handleLogin}>
      <LoginForm
        error={loginError}
        formFields={LoginFormFields}
        errorText={intl.formatMessage(messages.loginInvalidCredsError)}
        submitText={intl.formatMessage(messages.login)}
      />
    </Formik>
  );
};

export default Login;
