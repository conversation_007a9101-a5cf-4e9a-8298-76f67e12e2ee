import {
  PeekBox,
  Peek<PERSON>hip,
  PeekDivider,
  PeekGrid,
  PeekLoader,
  PeekPaper,
  PeekStack,
  PeekTooltip,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { InsightsAmenitiesAndFeatures, InsightsProperty, useAnalytics } from "@piiqtechnologies/ui-lib";
import React, { useMemo, useState } from "react";
import { StatusIndicator, StatusLevel } from "@common-components/status-indicator";
import NoDataFound from "@common-components/no-data-found";
import AmenitiesHeader from "./AmenitiesHeader";

export interface AmenitiesInsightsTableProps {
  title: string;
  data: InsightsAmenitiesAndFeatures[];
  selectedProperties: InsightsProperty[];
  showFilters?: boolean;
  isLoading?: boolean;
  error?: boolean;
}

const FilterChip: React.FC<{
  label: string;
  color: "success" | "warning" | "error" | "default";
  active?: boolean;
  onClick?: () => void;
}> = ({ label, color, active = false, onClick }) => {
  const theme = useTheme();

  return (
    <PeekChip
      label={label}
      variant={active ? "filled" : "outlined"}
      color={color}
      size="small"
      onClick={onClick}
      sx={{
        fontSize: theme.typography.body2.fontSize,
        height: 24,
        cursor: onClick ? "pointer" : "default",
      }}
    />
  );
};

const DEMAND_LEVELS: StatusLevel[] = ["high", "medium", "low", "na"];
const DEMAND_LEVELS_MAPPING: Record<
  StatusLevel,
  { label: string; color: "success" | "warning" | "error" | "default" }
> = {
  high: {
    label: "High",
    color: "success",
  },
  medium: {
    label: "Medium",
    color: "warning",
  },
  low: {
    label: "Low",
    color: "error",
  },
  na: {
    label: "N/A",
    color: "default",
  },
};

export const AmenitiesInsightsTable: React.FC<AmenitiesInsightsTableProps> = ({
  title,
  data,
  selectedProperties,
  isLoading = false,
  error,
}) => {
  const theme = useTheme();
  const [selectedDemandFilters, setSelectedDemandFilters] = useState<StatusLevel[]>([]);
  const analytics = useAnalytics();

  const amenities = useMemo(() => {
    const amenities = [];
    data?.forEach(({ name, properties }) => {
      // filter out amenities that are not in the selected demand filters
      const filteredProperties = Object.values(properties).filter(
        (propertyAmenities) =>
          selectedDemandFilters.length === 0 ||
          propertyAmenities.some((amenity) =>
            selectedDemandFilters.includes((amenity.interest?.toLowerCase() || "na") as StatusLevel),
          ),
      );
      if (filteredProperties.length > 0) {
        amenities.push(name);
      }
    });
    return amenities;
  }, [data, selectedDemandFilters]);

  return (
    <PeekPaper sx={{ p: 2 }}>
      <PeekStack direction="column" justifyContent="space-between" alignItems="center" spacing={3}>
        <PeekStack spacing={1} width="100%">
          <PeekStack direction="row" spacing={1} justifyContent="space-between" alignItems="center" width="100%">
            <PeekTypography variant="h5" fontWeight="fontWeightBold">
              {title}
            </PeekTypography>
            <PeekStack direction="row" spacing={1} justifyContent="flex-end" alignItems="center">
              <PeekTypography variant="body2" color="GrayText">
                Demand Correlation:
              </PeekTypography>
              <PeekStack direction="row" spacing={0.5}>
                {DEMAND_LEVELS.map((level) => {
                  const isActive = selectedDemandFilters.includes(level as StatusLevel);
                  return (
                    <FilterChip
                      key={level}
                      {...DEMAND_LEVELS_MAPPING[level as StatusLevel]}
                      active={isActive}
                      onClick={() => {
                        setSelectedDemandFilters((prev) =>
                          isActive ? prev.filter((f) => f !== (level as StatusLevel)) : [...prev, level as StatusLevel],
                        );
                        analytics?.track("amenities_demand_filter_clicked", {
                          level,
                          active: !isActive,
                        });
                      }}
                    />
                  );
                })}
              </PeekStack>
            </PeekStack>
          </PeekStack>
          <PeekDivider light orientation="horizontal" />
        </PeekStack>
        {isLoading && <PeekLoader />}
        {!isLoading && data.length === 0 && <NoDataFound message="No Amenities Data Available" />}
        {!isLoading && error && <NoDataFound error message="Could not load insights data. Please try again later." />}
        {!isLoading && data.length > 0 && (
          <PeekGrid container spacing={1.5}>
            <PeekGrid item xs={1.5}>
              <AmenitiesHeader amenities={amenities} />
            </PeekGrid>
            {selectedProperties.map((property) => {
              return (
                <PeekGrid item xs={2.1} key={property.id}>
                  <PeekPaper sx={{ backgroundColor: "grey.50", height: "100%", position: "relative" }}>
                    <PeekStack direction="column" alignItems="center" spacing={1} sx={{ p: 1 }}>
                      <PeekTooltip title={property.name} placement="top">
                        <PeekTypography
                          variant="h4"
                          color="text.disabled"
                          fontWeight="fontWeightMedium"
                          textTransform={"uppercase"}
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          {property.name}
                        </PeekTypography>
                      </PeekTooltip>
                      <PeekGrid direction="column" container rowSpacing={1}>
                        {amenities.map((amenity) => {
                          const amenityData = Object.values(data?.find(({ name }) => name === amenity).properties).find(
                            (propertyAmenities) => propertyAmenities.some((a) => a.communityId === property.id),
                          );
                          return (
                            <PeekGrid xs={12} item justifyContent="center" minHeight={48}>
                              <PeekStack direction="row" alignItems="center" justifyContent="center">
                                {(!!amenityData && (
                                  <PeekStack direction="row" alignItems="center" spacing={0.5}>
                                    {amenityData.map(({ interest, virtualTourUrl, tourLabel, name }) => {
                                      const status = (interest?.toLowerCase() || "na") as StatusLevel;
                                      return (
                                        <PeekTooltip title={tourLabel || amenity} key={`${name}-${tourLabel}`}>
                                          <PeekBox>
                                            <StatusIndicator
                                              status={status.toLocaleLowerCase() as StatusLevel}
                                              size={20}
                                              virtualTourUrl={virtualTourUrl || ""}
                                              amenityName={tourLabel || amenity}
                                              propertyName={name}
                                            />
                                          </PeekBox>
                                        </PeekTooltip>
                                      );
                                    })}
                                  </PeekStack>
                                )) || (
                                  <PeekTypography variant="body1" color="text.disabled" fontWeight={"fontWeightMedium"}>
                                    -
                                  </PeekTypography>
                                )}
                              </PeekStack>
                            </PeekGrid>
                          );
                        })}
                      </PeekGrid>
                    </PeekStack>
                  </PeekPaper>
                </PeekGrid>
              );
            })}
          </PeekGrid>
        )}
      </PeekStack>
    </PeekPaper>
  );
};

export default AmenitiesInsightsTable;
