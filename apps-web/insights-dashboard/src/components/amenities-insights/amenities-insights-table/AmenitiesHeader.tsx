import { PeekGrid, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import React from "react";

const AmenityText = ({ text }: { text: string }) => (
  <PeekGrid xs={12} item justifyContent="flex-start" alignItems="center" minHeight={48}>
    <PeekTypography
      variant="body1"
      color="text.primary"
      fontWeight={"fontWeightMedium"}
      sx={{
        overflow: "hidden",
        textOverflow: "ellipsis",
        display: "-webkit-box",
        WebkitLineClamp: "2",
        WebkitBoxOrient: "vertical",
      }}
    >
      {text}
    </PeekTypography>
  </PeekGrid>
);

export const AmenitiesHeader = ({ amenities }: { amenities: string[] }) => {
  return (
    <PeekStack direction="column" spacing={1} sx={{ p: 1 }}>
      <PeekStack direction="row" alignItems="center">
        <PeekTypography variant="h4" color="text.disabled" fontWeight="fontWeightMedium" textTransform={"uppercase"}>
          Amenities
        </PeekTypography>
      </PeekStack>
      <PeekGrid direction="column" container rowSpacing={1}>
        {amenities.map((amenity, index) => (
          <AmenityText key={index} text={amenity} />
        ))}
      </PeekGrid>
    </PeekStack>
  );
};

export default AmenitiesHeader;
