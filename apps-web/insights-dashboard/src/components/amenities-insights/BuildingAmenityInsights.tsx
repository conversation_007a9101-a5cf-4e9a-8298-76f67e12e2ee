import { InsightsContext } from "@layouts/insights";
import { useError, useInsightsAmenitiesAndFeatures } from "@piiqtechnologies/ui-lib";
import React, { useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { format } from "date-fns";
import { AmenitiesInsightsTable } from "./amenities-insights-table";
import { Period } from "@components/common/date-range-picker";

export const BuildingAmenityInsights: React.FC<{ dateRange: Period }> = ({ dateRange }) => {
  const [fetchAmenitiesAndFeatures, amenitiesAndFeatures, loadingAmenityAndUnits, errorAmenityAndUnits] =
    useInsightsAmenitiesAndFeatures();
  const { insightsFilters, selectedProperties } = useOutletContext<InsightsContext>();
  const { setError } = useError();

  useEffect(() => {
    const ids = selectedProperties?.map((property) => property.id);
    if (ids.length > 0) {
      const formattedDateRange = {
        startDate: format(dateRange.startDate, "yyyy-MM-dd"),
        endDate: format(dateRange.endDate, "yyyy-MM-dd"),
      };
      fetchAmenitiesAndFeatures(
        {
          dateRange: formattedDateRange,
        },
        ids,
      );
    }
  }, [insightsFilters, selectedProperties, dateRange]);

  useEffect(() => {
    if (errorAmenityAndUnits?.message) {
      setError(errorAmenityAndUnits?.message);
    }
  }, [errorAmenityAndUnits]);

  return (
    <AmenitiesInsightsTable
      title="BUILDING AMENITY INSIGHTS"
      selectedProperties={selectedProperties}
      data={amenitiesAndFeatures ?? []}
      showFilters={true}
      isLoading={loadingAmenityAndUnits}
      error={!!errorAmenityAndUnits?.message}
    />
  );
};
