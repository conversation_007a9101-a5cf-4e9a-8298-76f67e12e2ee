import { PropertyMapPin, PrpopertyMapPinFilled } from "@piiqtechnologies/svg-icons";
import { PeekTooltip, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import { InsightsProperty, useAnalytics } from "@piiqtechnologies/ui-lib";
import { AdvancedMarker } from "@vis.gl/react-google-maps";
import React from "react";
import PropertyLocationMarkerTooltip from "./PropertyLocationMarkerTooltip";

interface PropertyLocationMarkerProps {
  onSelectProperty: (propertyId: string) => void;
  property: InsightsProperty;
  selectedIndex: number;
  hovered: boolean;
  onHoveredChange: (hovered: boolean) => void;
}

const PropertyLocationMarker: React.FC<PropertyLocationMarkerProps> = ({
  onSelectProperty,
  property,
  selectedIndex,
  hovered,
  onHoveredChange,
}) => {
  const theme = useTheme();
  const propertyTypeColor =
    property.propertyType === "peek"
      ? theme.palette.teal
      : property.hasPrice
        ? theme.palette.purple
        : theme.palette.orange;
  const analytics = useAnalytics();
  return (
    <PeekTooltip
      arrow
      title={
        <PropertyLocationMarkerTooltip
          property={property}
          onClose={() => onHoveredChange(false)}
          onSelectProperty={(propertyId) => {
            if (property.propertyType !== "peek" && !property.hasPrice) {
              onHoveredChange(false);
              analytics.track("property_request_data_clicked", {
                propertyId,
                propertyName: property.name,
              });
              return;
            }
            onSelectProperty(propertyId);
            analytics.track("property_location_marker_selected", {
              propertyId,
              propertyName: property.name,
            });
            onHoveredChange(false);
          }}
        />
      }
      open={hovered}
      placement="top"
      PopperProps={{
        disablePortal: true,
      }}
      sx={{
        "& .MuiTooltip-arrow": {
          color: "common.white",
        },
        ".MuiTooltip-tooltip": {
          maxWidth: 330,
        },
      }}
    >
      <AdvancedMarker
        position={property.point}
        onClick={() => {
          onHoveredChange(!hovered);
          analytics.track("property_location_marker_clicked", {
            propertyId: property.id,
            propertyName: property.name,
          });
        }}
      >
        {(selectedIndex === -1 && (
          <PeekTypography color={propertyTypeColor[400]} lineHeight={0}>
            <PropertyMapPin width={50} height={50} hovered={hovered} />
          </PeekTypography>
        )) || (
          <PeekTypography color={propertyTypeColor[100]} lineHeight={0}>
            <PrpopertyMapPinFilled
              width={60}
              height={60}
              text={(selectedIndex + 1).toString()}
              textColor={propertyTypeColor[400]}
            />
          </PeekTypography>
        )}
      </AdvancedMarker>
    </PeekTooltip>
  );
};

export default PropertyLocationMarker;
