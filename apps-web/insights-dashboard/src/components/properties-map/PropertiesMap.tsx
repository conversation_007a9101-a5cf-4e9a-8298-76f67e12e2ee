import { PeekBox, useTheme } from "@piiqtechnologies/ui-components";
import { InsightsProperty } from "@piiqtechnologies/ui-lib";
import { isEmptyLocation } from "@utils";
import { APIProvider, Map, MapCameraChangedEvent, MapCameraProps, useMap } from "@vis.gl/react-google-maps";
import React, { useCallback, useEffect, useState } from "react";
import { NoLocation } from "./NoLocation";
import PropertyLocationMarker from "./PropertyLocationMarker";

export interface PropertiesMapProps {
  properties: InsightsProperty[];
  location?: google.maps.LatLngLiteral;
  onSelectProperty: (propertyId: string) => void;
  selectedProperties: string[];
}

const FitBounds = ({
  location,
  properties,
}: {
  location?: google.maps.LatLngLiteral;
  properties: InsightsProperty[];
}) => {
  const map = useMap();

  useEffect(() => {
    if (!map) return;
    if (properties && properties.length > 0 && !isEmptyLocation(location)) {
      // Use bounds to fit all properties
      const bounds = new google.maps.LatLngBounds();
      bounds.extend(location);
      properties.forEach((property) => {
        if (property.point && property.point.lat && property.point.lng) {
          bounds.extend({ lat: property.point.lat, lng: property.point.lng });
        }
      });
      const center = bounds.getCenter();
      map.setCenter(center);
      map.fitBounds(bounds, 0);
    }
  }, [map, location, properties]);

  return null;
};

const INITIAL_MAP_CAMERA = {
  center: { lat: 40.7127753, lng: -74.0059728 },
  zoom: 10,
};

export const PropertiesMap: React.FC<PropertiesMapProps> = ({
  location,
  properties,
  onSelectProperty,
  selectedProperties,
}) => {
  const theme = useTheme();
  const [hoveredPropertyId, setHoveredPropertyId] = useState<string | null>(null);
  const [cameraProps, setCameraProps] = useState<MapCameraProps>(INITIAL_MAP_CAMERA);

  const handleCameraChange = useCallback((ev: MapCameraChangedEvent) => {
    setCameraProps(ev.detail);
  }, []);

  useEffect(() => {
    if (!isEmptyLocation(location)) {
      setCameraProps({
        center: location,
        zoom: 12,
      });
    }
  }, [location]);

  return (
    <APIProvider
      apiKey={process.env.PEEK_APP_GOOGLE_MAPS_API_KEY! as string}
      version={process.env.PEEK_APP_GOOGLE_MAPS_API_VERSION! as string}
    >
      <Map
        onCameraChanged={handleCameraChange}
        onBoundsChanged={handleCameraChange}
        onZoomChanged={handleCameraChange}
        onDrag={handleCameraChange}
        {...cameraProps}
        gestureHandling="cooperative"
        mapTypeId="roadmap"
        disableDefaultUI={true}
        mapId={process.env.PEEK_APP_GOOGLE_MAPS_MAP_ID! as string}
        zoomControl={true}
        zoomControlOptions={{
          position: google.maps.ControlPosition.RIGHT_BOTTOM,
        }}
        fullscreenControl={true}
        fullscreenControlOptions={{
          position: google.maps.ControlPosition.RIGHT_BOTTOM,
        }}
        styles={[
          {
            featureType: "administrative",
            elementType: "geometry",
            stylers: [
              {
                visibility: "off",
              },
            ],
          },
          {
            featureType: "poi",
            stylers: [
              {
                visibility: "off",
              },
            ],
          },
          {
            featureType: "road",
            elementType: "labels.icon",
            stylers: [
              {
                visibility: "off",
              },
            ],
          },
          {
            featureType: "transit",
            stylers: [
              {
                visibility: "off",
              },
            ],
          },
        ]}
      >
        {isEmptyLocation(location) && (
          <PeekBox
            sx={{
              height: "100%",
              width: "100%",
              top: "0px",
              left: "0px",
              position: "absolute",
              backgroundColor: theme.palette.grey[50],
              opacity: 0.8,
              zIndex: 1000,
            }}
          >
            <NoLocation />
          </PeekBox>
        )}
        <FitBounds location={location} properties={properties} />
        {properties.map((property) => (
          <PropertyLocationMarker
            key={property.id}
            onSelectProperty={onSelectProperty}
            property={property}
            selectedIndex={selectedProperties.findIndex((id) => id === property.id)}
            hovered={hoveredPropertyId === property.id}
            onHoveredChange={(hovered) => setHoveredPropertyId(hovered ? property.id : null)}
          />
        ))}
      </Map>
    </APIProvider>
  );
};

export default PropertiesMap;
