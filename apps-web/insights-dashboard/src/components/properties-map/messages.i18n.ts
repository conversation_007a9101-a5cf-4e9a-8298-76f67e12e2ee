import { defineMessages } from "react-intl";

const messages = defineMessages({
  addProperty: {
    id: "app.propertiesMap.addProperty.text",
    defaultMessage: "Add Property",
  },
  yearBuilt: {
    id: "app.propertiesMap.yearBuilt.text",
    defaultMessage: "Year Built",
  },
  assetsClass: {
    id: "app.propertiesMap.assetsClass.text",
    defaultMessage: "Assets Class",
  },
  units: {
    id: "app.propertiesMap.units.text",
    defaultMessage: "# Units",
  },
  amenities: {
    id: "app.propertiesMap.amenities.text",
    defaultMessage: "# Amenities",
  },
  price: {
    id: "app.propertiesMap.price.text",
    defaultMessage: "Price",
  },
  virtualTour: {
    id: "app.propertiesMap.virtualTour.text",
    defaultMessage: "Virtual Tour",
  },
  peekProperty: {
    id: "app.propertiesMap.peekProperty.text",
    defaultMessage: "Peek Property",
  },
  noDataAvailable: {
    id: "app.propertiesMap.noDataAvailable.text",
    defaultMessage: "Data Unavailable: Create a request to pull property data",
  },
  requestData: {
    id: "app.propertiesMap.requestData.text",
    defaultMessage: "Request Data",
  },
});

export default messages;
