import SearchText from "@components/common/search-text";
import { PeekAutocomplete } from "@piiqtechnologies/ui-components";
import { PropertyLocation, useAnalytics } from "@piiqtechnologies/ui-lib";
import React, { useEffect, useMemo, useRef } from "react";
import PlacesAutocomplete, { Suggestion, geocodeByAddress, getLatLng } from "react-places-autocomplete";

export interface PropertiesLocationSearchProps {
  onPlaceSelect: (location: PropertyLocation) => void;
}

const persistSearchValue = (value: string) => {
  try {
    localStorage.setItem("peek_location_search_value", value);
  } catch (error) {
    console.error("Failed to persist search value:", error);
  }
};

const getSavedSearchValue = (): string => {
  const defaultValue = "Atlanta, GA, USA"; // Default value if nothing is saved
  try {
    return localStorage.getItem("peek_location_search_value") || defaultValue; // Default to Atlanta if no value is saved
  } catch (error) {
    console.error("Failed to retrieve saved search value:", error);
    return defaultValue;
  }
};

export const PropertiesLocationSearch: React.FC<PropertiesLocationSearchProps> = ({ onPlaceSelect }) => {
  // Initialize searchValue with persisted value or empty string
  const [searchValue, setSearchValue] = React.useState(getSavedSearchValue());

  const analytics = useAnalytics();

  const saveSearchValue = (search: string) => {
    setSearchValue(search);
    persistSearchValue(search);
  };

  const setDefaultLocation = async (search: string) => {
    try {
      const [address] = await geocodeByAddress(search);
      const { lat, lng } = await getLatLng(address);
      onPlaceSelect({ lat, lng });
    } catch (error) {
      console.error("Failed to set default location:", error);
    }
  };

  // Auto-select Atlanta or restore persisted location
  useEffect(() => {
    const savedValue = getSavedSearchValue();
    setSearchValue(savedValue);
    setDefaultLocation(savedValue);
    analytics.track("default_location_set", {
      description: savedValue,
    });
  }, []);

  const handleAddressChange = async (_event: any, suggestion: Suggestion) => {
    if (!suggestion?.description) {
      return;
    }
    const [address] = await geocodeByAddress(suggestion?.description);
    const { lat, lng } = await getLatLng(address);
    saveSearchValue(suggestion.description);
    onPlaceSelect({ lat, lng });
    analytics.track("property_location_searched", {
      description: suggestion.description,
      lat,
      lng,
    });
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchValue(value);
    // Don't persist on every keystroke, only on selection
  };

  const auxSearchOptions = useMemo(() => {
    const defaultOptions = {
      componentRestrictions: {
        country: ["us"],
      },
      types: ["locality", "postal_code", "administrative_area_level_1", "neighborhood"],
    };

    return defaultOptions;
  }, []);

  const defaultSuggestions = useMemo(
    () => [
      {
        description: "Atlanta, GA, USA",
        place_id: "ChIJ5dDpeSqKFIgRXdpKHnQvKKM",
        structured_formatting: {
          main_text: "Atlanta",
          secondary_text: "GA, USA",
        },
      },
    ],
    [],
  );

  return (
    //@ts-ignore
    <PlacesAutocomplete
      searchOptions={auxSearchOptions}
      value={searchValue || ""}
      //@ts-ignore
      onChange={(address) => setSearchValue(address)}
      debounce={1000}
    >
      {({ suggestions, loading, getInputProps }) => {
        const inputProps = getInputProps();
        return (
          <PeekAutocomplete
            freeSolo={false}
            popupIcon={null}
            sx={{
              ".MuiAutocomplete-input": { pl: "7px !important" },
              ".MuiAutocomplete-inputRoot": { pr: "0 !important" },
            }}
            options={suggestions || defaultSuggestions}
            loading={loading}
            getOptionLabel={(suggestion: Suggestion) => suggestion?.description || ""}
            onChange={handleAddressChange}
            onInputChange={(_event: any, newInputValue: string) => {
              setSearchValue(newInputValue);
              inputProps.onChange({ target: { value: newInputValue } });
            }}
            renderInput={(params: any) => (
              <SearchText
                {...params}
                searchText={searchValue}
                onChange={handleInputChange}
                placeholder="Search & Select a Metro Area"
              />
            )}
            isOptionEqualToValue={(option: Suggestion, value: Suggestion) => option?.description === value?.description}
            noOptionsText="Start typing to search for a metro area..."
          />
        );
      }}
    </PlacesAutocomplete>
  );
};

export default PropertiesLocationSearch;
