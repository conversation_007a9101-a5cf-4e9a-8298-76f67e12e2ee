import { Buildings, Check, X } from "@piiqtechnologies/svg-icons";
import {
  PeekBox,
  PeekButton,
  PeekChip,
  PeekDivider,
  PeekGrid,
  PeekIconButton,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { InsightsProperty } from "@piiqtechnologies/ui-lib";
import React, { SVGProps } from "react";
import { useIntl } from "react-intl";
import messages from "./messages.i18n";

interface PropertyLocationMarkerTooltipProps {
  onSelectProperty: (propertyId: string) => void;
  property: InsightsProperty;
  onClose: () => void;
}

const PropertyInformationText = ({ label, value }: { label: string; value: string | number }) => (
  <PeekStack direction="row" spacing={0.5} alignItems="center">
    <PeekTypography variant="body2" color="text.disabled">
      {label}:
    </PeekTypography>
    <PeekTypography variant="body2" color="text.primary">
      {value || "-"}
    </PeekTypography>
  </PeekStack>
);

const PropertyTypeChip = ({
  Icon,
  color,
  label,
}: {
  Icon?: React.FC<SVGProps<SVGSVGElement>>;
  color: Record<string, string>;
  label: string;
}) => {
  const theme = useTheme();
  return (
    <PeekChip
      label={label}
      icon={(!!Icon && <Icon />) || null}
      variant="outlined"
      size="small"
      sx={{
        p: 0,
        pl: 0.4,
        color: color[400],
        borderColor: color[400],
        backgroundColor: `${color[50]} !important`,
        ...theme.typography.body2,
        "& .MuiChip-icon": {
          color: color[400],
          width: 12,
        },
      }}
    />
  );
};

const PropertyLocationMarkerTooltip: React.FC<PropertyLocationMarkerTooltipProps> = ({
  onSelectProperty,
  property,
  onClose,
}) => {
  const theme = useTheme();
  const intl = useIntl();
  const isNoDataAvailable = !property.hasPrice && property.propertyType !== "peek";
  const propertyTypeColor =
    property.propertyType === "peek"
      ? theme.palette.teal
      : isNoDataAvailable
        ? theme.palette.orange
        : theme.palette.purple;
  const getYear = (date: string) => {
    if (!date) return "-";
    const d = new Date(date);
    return d.getFullYear();
  };
  return (
    <PeekGrid container spacing={1} sx={{ pr: 1 }}>
      <PeekIconButton
        bordered={false}
        sx={{
          position: "absolute",
          top: 0,
          right: 0,
          zIndex: 1,
          color: theme.palette.primary.main,
        }}
        onClick={onClose}
      >
        <X height={14} width={14} />
      </PeekIconButton>
      <PeekGrid item xs={12} container spacing={0.5} alignItems="center">
        <PeekGrid item xs={7}>
          <PeekStack direction="column" spacing={0.5}>
            <PeekTypography variant="body1" fontWeight="fontWeightMedium" color={theme.palette.text.primary}>
              {property.name}
            </PeekTypography>
            <PeekTypography variant="body2" color={theme.palette.text.disabled}>
              {property.address}
            </PeekTypography>
          </PeekStack>
        </PeekGrid>
        <PeekGrid item xs={3}>
          <PeekButton
            variant="outlined"
            onClick={() => onSelectProperty(property.id)}
            sx={{
              minWidth: 120,
              borderColor: propertyTypeColor[400],
              color: propertyTypeColor[400],
              "&:hover": {
                borderColor: propertyTypeColor[400],
                backgroundColor: propertyTypeColor[50],
              },
            }}
          >
            {intl.formatMessage((!isNoDataAvailable && messages.addProperty) || messages.requestData)}
          </PeekButton>
        </PeekGrid>
      </PeekGrid>
      <PeekGrid item xs={12} container direction="row" columnSpacing={0.7}>
        <PeekGrid item>
          <PropertyInformationText label={intl.formatMessage(messages.yearBuilt)} value={getYear(property.yearBuilt)} />
        </PeekGrid>
        <PeekGrid item xs={0.1}>
          <PeekDivider orientation="vertical" light />
        </PeekGrid>
        <PeekGrid item>
          <PropertyInformationText label={intl.formatMessage(messages.assetsClass)} value={property.assetClass} />
        </PeekGrid>
        <PeekGrid item xs={0.1}>
          <PeekDivider orientation="vertical" light />
        </PeekGrid>
        <PeekGrid item>
          <PropertyInformationText label={intl.formatMessage(messages.units)} value={property.unitCount} />
        </PeekGrid>
        <PeekGrid item xs={0.1}>
          <PeekDivider orientation="vertical" light />
        </PeekGrid>
        <PeekGrid item>
          <PropertyInformationText label={intl.formatMessage(messages.amenities)} value={property.amenitiesCount} />
        </PeekGrid>
      </PeekGrid>
      <PeekGrid item xs={12}>
        {(isNoDataAvailable && (
          <PropertyTypeChip label={intl.formatMessage(messages.noDataAvailable)} color={propertyTypeColor} />
        )) || (
          <PeekStack direction="row" spacing={1}>
            <PropertyTypeChip
              label={intl.formatMessage(messages.peekProperty)}
              Icon={property.propertyType === "peek" ? Check : X}
              color={propertyTypeColor}
            />
            <PropertyTypeChip
              label={intl.formatMessage(messages.price)}
              Icon={property.hasPrice ? Check : X}
              color={propertyTypeColor}
            />
            <PropertyTypeChip
              label={intl.formatMessage(messages.virtualTour)}
              Icon={property.hasTour ? Check : X}
              color={propertyTypeColor}
            />
          </PeekStack>
        )}
      </PeekGrid>
    </PeekGrid>
  );
};

export default PropertyLocationMarkerTooltip;
