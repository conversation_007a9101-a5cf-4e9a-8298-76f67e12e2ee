import { defineMessages } from "react-intl";

const messages = defineMessages({
  propertiesOverview: {
    id: "app.propertiesOverview.propertyOverview.text",
    defaultMessage: "Property Overview",
  },
  address: {
    id: "app.propertiesOverview.address.text",
    defaultMessage: "Address",
  },
  amenitiesCount: {
    id: "app.propertiesOverview.amenitiesCount.text",
    defaultMessage: "No of Amenities",
  },
  unitsCount: {
    id: "app.propertiesOverview.unitsCount.text",
    defaultMessage: "No of Units",
  },
  propertyType: {
    id: "app.propertiesOverview.propertyType.text",
    defaultMessage: "Property Type",
  },
  owner: {
    id: "app.propertiesOverview.owner.text",
    defaultMessage: "Owner",
  },
  manager: {
    id: "app.propertiesOverview.manager.text",
    defaultMessage: "Manager",
  },
  builtRenovated: {
    id: "app.propertiesOverview.builtRenovated.text",
    defaultMessage: "Built/Renovated",
  },
  addProperty: {
    id: "app.propertiesOverview.addProperty.text",
    defaultMessage: "Add Property",
  },
  add: {
    id: "app.propertiesOverview.add.text",
    defaultMessage: "Add",
  },
  removeProperty: {
    id: "app.propertiesOverview.removeProperty.text",
    defaultMessage: "Remove Property",
  },
});

export default messages;
