import { PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import React from "react";
import { useIntl } from "react-intl";
import messages from "./messages.i18n";

const PropertyDetailsText = ({ text }: { text: string }) => (
  <PeekStack direction="row" justifyContent="flex-start" alignItems="center" height={45}>
    <PeekTypography variant="body1" color="text.primary" fontWeight={"fontWeightMedium"}>
      {text}
    </PeekTypography>
  </PeekStack>
);

export const PropertiesOverviewHeader = () => {
  const intl = useIntl();
  return (
    <PeekStack direction="column" spacing={3} sx={{ p: 2 }}>
      <PeekStack direction="row" alignItems="center" justifyContent="center" height={70} width={100}>
        <PeekTypography variant="h3" color="text.disabled" fontWeight="fontWeightMedium" textTransform={"uppercase"}>
          {intl.formatMessage(messages.propertiesOverview)}
        </PeekTypography>
      </PeekStack>
      <PeekStack direction="column">
        <PropertyDetailsText text={intl.formatMessage(messages.address)} />
        <PropertyDetailsText text={intl.formatMessage(messages.amenitiesCount)} />
        <PropertyDetailsText text={intl.formatMessage(messages.unitsCount)} />
        <PropertyDetailsText text={intl.formatMessage(messages.propertyType)} />
        <PropertyDetailsText text={intl.formatMessage(messages.owner)} />
        <PropertyDetailsText text={intl.formatMessage(messages.manager)} />
        <PropertyDetailsText text={intl.formatMessage(messages.builtRenovated)} />
      </PeekStack>
    </PeekStack>
  );
};

export default PropertiesOverviewHeader;
