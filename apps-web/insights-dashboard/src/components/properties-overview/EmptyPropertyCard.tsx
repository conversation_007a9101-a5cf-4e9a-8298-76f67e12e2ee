import { InsightsContext } from "@layouts";
import { useTheme } from "@mui/material";
import { Plus as AddIcon, Camera2 as CameraIcon, Search } from "@piiqtechnologies/svg-icons";
import {
  PeekAutocomplete,
  PeekBox,
  PeekButton,
  PeekInputAdornment,
  PeekStack,
  PeekTextField,
  PeekTypography,
} from "@piiqtechnologies/ui-components";
import { InsightsProperty, useAnalytics } from "@piiqtechnologies/ui-lib";
import React, { useState } from "react";
import { useIntl } from "react-intl";
import { useOutletContext } from "react-router-dom";
import messages from "./messages.i18n";

interface EmptyPropertyCardProps {
  properties: InsightsProperty[];
  onAddProperty: (propertyId: string) => void;
  disabled?: boolean;
  selectedPropertyIds?: string[];
}

export const EmptyPropertyCard: React.FC<EmptyPropertyCardProps> = ({
  properties,
  onAddProperty,
  disabled = false,
  selectedPropertyIds = [],
}) => {
  const theme = useTheme();
  const intl = useIntl();
  const [showAutoComplete, setShowAutocomplete] = useState(false);
  const analytics = useAnalytics();
  const handleAddProperty = (_event: any, property: InsightsProperty) => {
    setShowAutocomplete(false);
    onAddProperty(property.id);
    analytics.track("property_added_from_overview", {
      propertyId: property.id,
      propertyName: property.name,
    });
  };

  const availableProperties = new Map();

  properties?.forEach((property) => {
    if (!selectedPropertyIds.includes(property.id) && !availableProperties.has(property.id)) {
      availableProperties.set(property.id, property);
    }
  });

  const propertyOptions = Array.from(availableProperties?.values());

  return (
    <PeekStack direction="column" sx={{ py: 2 }} alignItems="center" justifyContent="center" width="100%" height={70}>
      <PeekStack direction="column" alignItems="center" justifyContent="center">
        {(!showAutoComplete && (
          <PeekButton
            variant="outlined"
            disabled={disabled}
            fullWidth
            color="secondary"
            sx={{ borderRadius: 3, maxWidth: 150, width: 150, height: "auto", py: 0.5, ...theme.typography.body1 }}
            startIcon={<AddIcon height={17} width={17} />}
            onClick={() => setShowAutocomplete(true)}
          >
            {intl.formatMessage(messages.addProperty)}
          </PeekButton>
        )) || (
          <PeekAutocomplete
            height="auto"
            freeSolo={false}
            clearIcon={null}
            popupIcon={null}
            options={propertyOptions}
            disabled={disabled}
            onChange={handleAddProperty}
            sx={{
              width: "160px",
            }}
            isOptionEqualToValue={(option: InsightsProperty, value: InsightsProperty) =>
              option.id === value.id && option.name === value.name
            }
            getOptionLabel={({ name, address }: InsightsProperty) => `${name} (${address})`}
            renderInput={(params) => (
              <PeekTextField
                placeholder={"Search Property..."}
                {...params}
                variant="outlined"
                color="secondary"
                size="medium"
                InputProps={{
                  ...params.InputProps,
                  sx: { "&.MuiOutlinedInput-root": { py: 0 } },
                  startAdornment: (
                    <PeekInputAdornment position="start">
                      <Search />
                    </PeekInputAdornment>
                  ),
                }}
              />
            )}
          />
        )}
      </PeekStack>
    </PeekStack>
  );
};

export default EmptyPropertyCard;
