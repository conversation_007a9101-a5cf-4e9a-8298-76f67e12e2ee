import { Peek<PERSON>ox, PeekGrid, PeekPaper } from "@piiqtechnologies/ui-components";
import { InsightsProperty } from "@piiqtechnologies/ui-lib";
import React from "react";
import EmptyPropertyCard from "./EmptyPropertyCard";
import PropertyOverviewSidebar from "./PropertiesOverviewHeader";
import PropertyCard from "./PropertyCard";

interface PropertiesOverviewProps {
  properties: InsightsProperty[];
  onSelectProperty: (propertyId: string) => void;
  selectedProperties: InsightsProperty[];
  onRemoveProperty: (propertyId: string) => void;
}

export const PropertiesOverview: React.FC<PropertiesOverviewProps> = ({
  properties,
  onSelectProperty,
  selectedProperties,
  onRemoveProperty,
}) => {
  return (
    <PeekPaper sx={{ p: 1.5 }}>
      <PeekGrid container spacing={1.5}>
        <PeekGrid item xs={1.5} sx={{ position: "sticky", top: 0, zIndex: 10, alignSelf: "flex-start" }}>
          <PropertyOverviewSidebar />
        </PeekGrid>
        {Array.from({ length: 5 }).map((_, index) => {
          if (index >= selectedProperties?.length) {
            return (
              <PeekGrid item xs={2.1} key={index}>
                <PeekPaper sx={{ backgroundColor: "grey.50", height: "100%" }}>
                  <EmptyPropertyCard
                    properties={properties || []}
                    onAddProperty={onSelectProperty}
                    selectedPropertyIds={selectedProperties.map((p) => p.id)}
                    disabled={!properties?.length}
                  />
                </PeekPaper>
              </PeekGrid>
            );
          }
          return (
            <PeekGrid item xs={2.1} key={index}>
              <PeekPaper sx={{ backgroundColor: "grey.50", height: "100%" }}>
                <PropertyCard property={selectedProperties[index]} onRemoveProperty={onRemoveProperty} />
              </PeekPaper>
            </PeekGrid>
          );
        })}
      </PeekGrid>
    </PeekPaper>
  );
};

export default PropertiesOverview;
