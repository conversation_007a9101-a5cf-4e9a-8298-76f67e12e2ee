import { Buildings, X } from "@piiqtechnologies/svg-icons";
import {
  PeekBox,
  PeekButton,
  PeekContainedIconButton,
  PeekIconButton,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import { InsightsProperty, useAnalytics } from "@piiqtechnologies/ui-lib";
import React from "react";

// Utility function to format large numbers with commas
const formatNumber = (value: number | string): string => {
  if (!value && value !== 0) return "-";
  const numValue = typeof value === "string" ? parseInt(value) : value;
  if (isNaN(numValue)) return "-";

  if (numValue >= 1000) {
    return new Intl.NumberFormat("en-US").format(numValue);
  }
  return numValue.toString();
};

interface PropertyCardProps {
  property: InsightsProperty;
  onRemoveProperty?: (propertyId: string) => void;
}

const PropertyDetailsText = ({ text, children }: { text: string | number; children?: React.ReactNode }) => (
  <PeekStack direction="row" justifyContent="center" alignItems="center" height={45} spacing={1}>
    <PeekTypography variant="body1" color="text.primary">
      {text}
    </PeekTypography>
    {children}
  </PeekStack>
);

export const PropertyCard: React.FC<PropertyCardProps> = ({ property, onRemoveProperty }) => {
  const theme = useTheme();
  const isNoDataAvailable = !property.hasPrice && property.propertyType !== "peek";
  const propertyTypeColor =
    property.propertyType === "peek"
      ? theme.palette.teal
      : isNoDataAvailable
        ? theme.palette.orange
        : theme.palette.purple;
  const analytics = useAnalytics();
  const getYear = (date: string) => {
    if (!date) return "-";
    const d = new Date(date);
    return d.getFullYear();
  };

  return (
    <PeekStack direction="column" spacing={3} sx={{ py: 2, px: 1 }}>
      <PeekStack direction="row" alignItems="center" justifyContent="center" spacing={0.5} height={70}>
        <PeekButton
          variant="outlined"
          fullWidth
          color="secondary"
          sx={{
            borderRadius: 3,
            height: "auto",
            py: 0.5,
            ...theme.typography.body1,
            borderColor: propertyTypeColor[400],
            color: propertyTypeColor[400],
            "&:hover": {
              borderColor: propertyTypeColor[400],
              backgroundColor: propertyTypeColor[50],
            },
          }}
        >
          {property.name}
        </PeekButton>
        <PeekIconButton
          id="menu-button"
          color="primary"
          sx={{
            borderColor: propertyTypeColor[400],
            color: propertyTypeColor[400],
            "&:hover": {
              borderColor: propertyTypeColor[400],
              backgroundColor: propertyTypeColor[50],
            },
          }}
          onClick={() => {
            onRemoveProperty?.(property.id);
            analytics.track("property_removed", {
              propertyId: property.id,
              propertyName: property.name,
            });
          }}
        >
          <X height={14} width={14} />
        </PeekIconButton>
      </PeekStack>
      <PeekStack direction="column">
        <PropertyDetailsText text={property.address} />
        <PropertyDetailsText text={formatNumber(property.amenitiesCount)}></PropertyDetailsText>
        <PropertyDetailsText text={formatNumber(property.unitCount)}></PropertyDetailsText>
        <PropertyDetailsText text={property.assetClass} />
        <PropertyDetailsText text={property.owner} />
        <PropertyDetailsText text={property.manager} />
        <PropertyDetailsText text={getYear(property.yearBuilt)} />
      </PeekStack>
    </PeekStack>
  );
};

export default PropertyCard;
