import { AmenitiesSelector } from "@components/common/filters/AmenitiesSelector";
import { BuildingTypeSelector } from "@components/common/filters/BuildingTypeSelector";
import { FilterField, FilterFieldComponentProps } from "@components/common/filters/FilterField";
import { LayoutSelector } from "@components/common/filters/LayoutSelector";
import { SFSelector } from "@components/common/filters/SFSelector";
import { PeekGrid, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import React from "react";
import { UnitFeaturesSelector } from "@components/common/filters/UnitFeaturesSelector";

const allFilters: { name: string; label: string; FilterComponent: React.FC<FilterFieldComponentProps> }[] = [
  {
    name: "buildingType",
    label: "Building Type",
    FilterComponent: BuildingTypeSelector,
  },
  {
    name: "layout",
    label: "Layout",
    FilterComponent: LayoutSelector,
  },
  {
    name: "unitAmenities",
    label: "Unit Features",
    FilterComponent: UnitFeaturesSelector,
  },
  {
    name: "sf",
    label: "SqFt/Unit",
    FilterComponent: SFSelector,
  },
  {
    name: "amenities",
    label: "Building Amenities",
    FilterComponent: AmenitiesSelector,
  },
];

export const PropertiesAveragesFilters: React.FC<any> = ({ filters, setFilters }) => {
  return (
    <PeekStack direction="row" alignItems="center" spacing={0.5}>
      <PeekTypography variant="body1" fontWeight={500} width={120}>
        Report Filters:
      </PeekTypography>
      <PeekGrid container spacing={0.4} alignItems="center">
        {allFilters.map(({ name, label, FilterComponent }) => (
          <PeekGrid item key={name} md="auto">
            <FilterField
              name={name}
              label={label}
              value={filters[name]}
              onFilterChange={(name, value) => {
                setFilters({
                  ...filters,
                  [name]: value,
                });
              }}
              FilterComponent={FilterComponent}
            />
          </PeekGrid>
        ))}
      </PeekGrid>
    </PeekStack>
  );
};
