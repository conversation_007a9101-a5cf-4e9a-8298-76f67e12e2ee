import { Period } from "@components/common/date-range-picker";
import NoDataFound from "@components/common/no-data-found";
import { InsightsContext } from "@layouts";
import { useTheme } from "@mui/material/styles";
import {
  PeekBox,
  PeekDivider,
  PeekFormControl,
  PeekLoader,
  PeekMenuItem,
  PeekPaper,
  PeekSelect,
  PeekStack,
  PeekTypography,
} from "@piiqtechnologies/ui-components";
import { InsightsPropertiesAveragesResponse, useInsightsPropertyAverages } from "@piiqtechnologies/ui-lib";
import { format } from "date-fns";
import React, { useEffect, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";
import { CartesianGrid, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

type MetricType =
  | "Avg Asking Rent"
  | "Median Asking Rent"
  | "Avg Asking Rent PSF"
  | "Median Asking Rent PSF"
  | "Concessions %"
  | "Avg NER"
  | "Median NER"
  | "Avg NER PSF"
  | "Median NER PSF";

type DemandType = "Demand" | "Virtual Tours" | "Self-Guided Tours";

enum MetricTypeToKey {
  "Avg Asking Rent" = "avgAskingRent",
  "Median Asking Rent" = "medianAskingRent",
  "Avg Asking Rent PSF" = "avgAskingRentPSF",
  "Median Asking Rent PSF" = "medianAskingRentPSF",
  "Concessions %" = "concessionsPercent",
  "Avg NER" = "avgNER",
  "Median NER" = "medianNER",
  "Avg NER PSF" = "avgNERPSF",
  "Median NER PSF" = "medianNERPSF",
}

enum DemandTypeToKey {
  Demand = "demandIndex",
  "Virtual Tours" = "virtualTours",
  "Self-Guided Tours" = "selfGuidedTours",
}

export interface PropertyAveragesProps {
  dateRange: Period;
  graphFilters?: {
    buildingType: string[];
    layout: string[];
    unitAmenities: string[];
    sf: string[];
    amenities: string[];
  };
}

interface LineConfig {
  dataKey: string;
  stroke: string;
  strokeDasharray?: string;
  name: string;
  yAxisId: string;
}

interface LegendItem {
  name: string;
  color: string;
}

function roundToNearest(value: number, multiple: number): number {
  return Math.round(value / multiple) * multiple;
}

function applyPadding(value, percent, direction) {
  const padding = value * percent;
  return direction === "min" ? value - padding : value + padding;
}

const CustomLegendItem: React.FC<LegendItem> = ({ name, color }) => {
  return (
    <PeekStack direction="row" spacing={1} alignItems="center">
      <PeekBox
        sx={{
          width: 12,
          height: 12,
          borderRadius: "50%",
          backgroundColor: color,
          flexShrink: 0,
        }}
      />
      <PeekTypography
        variant="caption"
        color="text.primary"
        sx={{
          whiteSpace: "nowrap",
        }}
      >
        {name}
      </PeekTypography>
    </PeekStack>
  );
};

const CustomLegends: React.FC<{ items: LegendItem[] }> = ({ items }) => {
  // Separate Metro Average from other items
  const metroItem = items.find((item) => item.name === "Metro Average");
  const propertyItems = items.filter((item) => item.name !== "Metro Average");

  return (
    <PeekStack direction="row" spacing={3} alignItems="center" justifyContent="flex-start">
      {/* Property items */}
      <PeekStack direction="row" spacing={1} alignItems="center">
        {propertyItems.map((item, index) => (
          <CustomLegendItem key={index} name={item.name} color={item.color} />
        ))}
      </PeekStack>
      {!!propertyItems?.length && <PeekDivider orientation="vertical" flexItem light />}
      {/* Metro Average */}
      {metroItem && <CustomLegendItem name={metroItem.name} color={metroItem.color} />}
    </PeekStack>
  );
};

const PeekMetricSelector = ({
  selectedMetric,
  setSelectedMetric,
  metricOptions,
  borderLeftStyle = "solid",
}: {
  selectedMetric: MetricType | DemandType;
  setSelectedMetric: (metric: MetricType | DemandType) => void;
  metricOptions: { value: MetricType | DemandType; label: string }[];
  borderLeftStyle?: string;
}) => {
  // want left border for selected metric
  const theme = useTheme();
  return (
    <PeekFormControl size="small" sx={{ minWidth: 120 }}>
      <PeekSelect
        value={selectedMetric}
        color="secondary"
        onChange={(e) => setSelectedMetric(e.target.value as MetricType | DemandType)}
        displayEmpty
        sx={{
          "& .MuiOutlinedInput-notchedOutline": {
            border: "none",
          },
        }}
        renderValue={(value) => (
          <PeekStack
            direction="row"
            pl={1}
            alignItems="center"
            borderLeft={`1px ${borderLeftStyle} ${theme.palette.secondary.main}`}
          >
            <PeekTypography>{value}</PeekTypography>
          </PeekStack>
        )}
      >
        {metricOptions.map((option) => (
          <PeekMenuItem key={option.value} value={option.value}>
            {option.label}
          </PeekMenuItem>
        ))}
      </PeekSelect>
    </PeekFormControl>
  );
};

const CustomTooltip = ({ active, payload, label }: any) => {
  const theme = useTheme();

  const formatValue = (value: number, metricName: string) => {
    if (value === null || value === undefined) return "N/A";

    if (metricName.includes("Tours") || metricName.includes("Demand")) {
      return value.toString();
    } else if (metricName.includes("%")) {
      return `${value}%`;
    } else {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(value);
    }
  };

  if (active && payload && payload.length) {
    const filteredPayload = payload.filter((entry: any) => entry.value !== null && entry.value !== undefined);

    if (filteredPayload.length === 0) return null;

    return (
      <PeekBox
        sx={{
          backgroundColor: "white",
          padding: 2,
          borderRadius: 1,
          boxShadow: 2,
          border: `1px solid ${theme.palette.grey[300]}`,
        }}
      >
        <PeekTypography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
          {label}
        </PeekTypography>
        <PeekStack spacing={0.5}>
          {filteredPayload.map((entry: any, index: number) => (
            <PeekTypography key={index} variant="body2" sx={{ color: entry.color }}>
              {`${entry.name}: ${formatValue(entry.value, entry.name)}`}
            </PeekTypography>
          ))}
        </PeekStack>
      </PeekBox>
    );
  }

  return null;
};

export const PropertiesAverages: React.FC<PropertyAveragesProps> = ({ dateRange, graphFilters }) => {
  const theme = useTheme();
  const [selectedMetric, setSelectedMetric] = useState<MetricType>("Avg Asking Rent");
  const [selectedDemand, setSelectedDemand] = useState<DemandType>("Demand");
  const { insightsFilters, locationSelected, selectedProperties } = useOutletContext<InsightsContext>();

  // Local date range state for this component

  const [fetchPropertyAverage, propertyAverages, propertyAveragesLoading, propertyAveragesError] =
    useInsightsPropertyAverages();

  useEffect(() => {
    if (locationSelected) {
      // Convert Date objects to strings for API
      const formattedDateRange = {
        startDate: format(dateRange.startDate, "yyyy-MM-dd"),
        endDate: format(dateRange.endDate, "yyyy-MM-dd"),
      };

      const ids = selectedProperties?.map((property) => property.id);

      // Use local dateRange instead of insightsFilters.dateRange
      const filtersWithLocalDateRange = {
        ...insightsFilters,
        ids,
        dateRange: formattedDateRange,
        ...(graphFilters && {
          buildingType: graphFilters.buildingType,
          layout: graphFilters.layout,
          unitAmenities: graphFilters.unitAmenities,
          sf: graphFilters.sf,
          amenities: graphFilters.amenities,
        }),
      };
      fetchPropertyAverage(filtersWithLocalDateRange);
    }
  }, [insightsFilters, locationSelected, dateRange, graphFilters, selectedProperties]);

  const propertyAveragesData = propertyAverages as InsightsPropertiesAveragesResponse | null;

  const propertyAveragesDates = useMemo(() => {
    if (!propertyAveragesData || Object.keys(propertyAveragesData.metro)?.length < 1) return [];

    // Get all unique dates from metro averages
    const allDates = new Set<string>();
    Object.keys(propertyAveragesData.metro).forEach((date) => allDates.add(date));

    // Sort dates chronologically
    return Array.from(allDates).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
  }, [propertyAveragesData]);

  const marketData = useMemo(() => {
    if (!propertyAveragesData || Object.keys(propertyAveragesData.metro)?.length < 1) return { data: [] };
    let minMetricValue = Infinity;
    let maxMetricValue = -Infinity;
    let minDemandValue = Infinity;
    let maxDemandValue = -Infinity;

    const data = propertyAveragesDates.map((date) => {
      const data: Record<string, string> = { date };
      // Metro Average from API data
      const metroAverage = propertyAveragesData?.metro?.[date];
      if (metroAverage) {
        data["metroAverageMetrics"] = metroAverage[MetricTypeToKey[selectedMetric]];
        if (metroAverage[MetricTypeToKey[selectedMetric]]) {
          const metricValue = parseFloat(metroAverage[MetricTypeToKey[selectedMetric]]);
          minMetricValue = Math.min(minMetricValue, metricValue);
          maxMetricValue = Math.max(maxMetricValue, metricValue);
        }

        data["metroDemandIndex"] = metroAverage[DemandTypeToKey[selectedDemand]];
        if (metroAverage[DemandTypeToKey[selectedDemand]]) {
          // Parse to 2 decimal places for demand
          const demandValue = +parseFloat(metroAverage[DemandTypeToKey[selectedDemand]]).toFixed(2);
          minDemandValue = Math.min(minDemandValue, demandValue);
          maxDemandValue = Math.max(maxDemandValue, demandValue);
        }
      }

      // Add property data
      if (propertyAveragesData?.properties) {
        Object.entries(propertyAveragesData.properties).forEach(([propertyId, propertyData]) => {
          const propertyAverage = propertyData.averages[date];
          if (propertyAverage) {
            const metricValue = propertyAverage[MetricTypeToKey[selectedMetric]];
            // Only add property data if it has a valid value
            if (metricValue !== null && metricValue !== undefined) {
              data[`property_${propertyId}_metrics`] = metricValue;
              const numericValue = typeof metricValue === "string" ? parseFloat(metricValue) : metricValue;
              if (!isNaN(numericValue)) {
                minMetricValue = Math.min(minMetricValue, numericValue);
                maxMetricValue = Math.max(maxMetricValue, numericValue);
              }
            }
          }
        });
      }

      return data;
    });

    if (selectedMetric.includes("PSF")) {
      minMetricValue = roundToNearest(applyPadding(minMetricValue, 0.05, "min"), 0.5);
      maxMetricValue = roundToNearest(applyPadding(maxMetricValue, 0.05, "max"), 0.5);
    } else {
      minMetricValue = roundToNearest(applyPadding(minMetricValue, 0.05, "min"), 100);
      maxMetricValue = roundToNearest(applyPadding(maxMetricValue, 0.05, "max"), 100);
    }

    // Demand Handling
    minDemandValue = roundToNearest(applyPadding(minDemandValue, 0.1, "min"), 0.5);
    maxDemandValue = roundToNearest(applyPadding(maxDemandValue, 0.1, "max"), 0.5);

    return {
      data,
      minMetricValue,
      maxMetricValue,
      minDemandValue,
      maxDemandValue,
    };
  }, [selectedMetric, selectedDemand, propertyAveragesData, propertyAveragesDates]);

  // Define color palette for properties (max 5 properties + metro)
  const propertyColors = [
    theme.palette.graphColors.blue,
    theme.palette.graphColors.orange,
    theme.palette.graphColors.purple,
    theme.palette.graphColors.green,
    theme.palette.graphColors.yellow,
  ];

  const lineChartConfig = useMemo((): LineConfig[] => {
    const config: LineConfig[] = [];

    // Always add Metro Average
    config.push({
      dataKey: "metroAverageMetrics",
      stroke: theme.palette.secondary.main,
      name: "Metro Average",
      yAxisId: "left",
    });

    config.push({
      dataKey: "metroDemandIndex",
      strokeDasharray: "5 5",
      stroke: theme.palette.secondary.main,
      name: "Metro Demand",
      yAxisId: "right",
    });

    // Add property lines
    if (propertyAveragesData?.properties && Object.keys(propertyAveragesData.properties).length > 0) {
      Object.entries(propertyAveragesData.properties).forEach(([propertyId, propertyData], index) => {
        // Only add property line if it has data for the selected metric
        const hasPropertyDataForMetric = propertyAveragesDates.some((date) => {
          const propertyAverage = propertyData.averages[date];
          if (!propertyAverage) return false;
          const value = propertyAverage[MetricTypeToKey[selectedMetric]];
          return value !== null && value !== undefined;
        });

        if (hasPropertyDataForMetric) {
          config.push({
            dataKey: `property_${propertyId}_metrics`,
            stroke: propertyColors[index] || propertyColors[0], // fallback to first color
            name: propertyData.name,
            yAxisId: "left",
          });
        }
      });
    }

    return config;
  }, [theme, propertyAveragesData, selectedMetric, propertyAveragesDates, propertyColors]);

  // Generate legend items
  const legendItems = useMemo((): LegendItem[] => {
    const items: LegendItem[] = [];

    // Add properties to legend first
    if (propertyAveragesData?.properties && Object.keys(propertyAveragesData.properties).length > 0) {
      Object.entries(propertyAveragesData.properties).forEach(([propertyId, propertyData], index) => {
        // Only add to legend if property has data for the selected metric
        const hasPropertyDataForMetric = propertyAveragesDates.some((date) => {
          const propertyAverage = propertyData.averages[date];
          if (!propertyAverage) return false;

          const value = propertyAverage[MetricTypeToKey[selectedMetric]];
          return value !== null && value !== undefined;
        });

        if (hasPropertyDataForMetric) {
          items.push({
            name: propertyData.name,
            color: propertyColors[index] || propertyColors[0], // fallback to first color
          });
        }
      });
    }
    // Add Metro Average to legend (last)
    items.push({
      name: "Metro Average",
      color: theme.palette.secondary.main,
    });

    return items;
  }, [propertyAveragesData, selectedMetric, propertyAveragesDates, propertyColors]);

  const hasDataForMetric = (metricKey: string) => {
    if (!propertyAveragesData || Object.keys(propertyAveragesData.metro)?.length < 1) return false;

    // Special logic for NER metrics - only show if ALL items have the field
    const isNERMetric = metricKey.includes("NER");

    if (isNERMetric) {
      // Check that ALL metro data has NER fields
      const allMetroHasNER = propertyAveragesDates.every((date) => {
        const metroAverage = propertyAveragesData?.metro?.[date];
        if (!metroAverage) return false;
        const value = metroAverage[MetricTypeToKey[metricKey]];
        return value !== undefined && value !== null;
      });

      // Check that ALL properties have NER fields
      let allPropertiesHaveNER = true;
      if (propertyAveragesData?.properties && Object.keys(propertyAveragesData.properties).length > 0) {
        allPropertiesHaveNER = Object.values(propertyAveragesData.properties).every((propertyData) => {
          return propertyAveragesDates.every((date) => {
            const propertyAverage = propertyData.averages[date];
            if (!propertyAverage) return false;
            const value = propertyAverage[MetricTypeToKey[metricKey]];
            return value !== undefined && value !== null;
          });
        });
      }

      return allMetroHasNER && allPropertiesHaveNER;
    }

    // Original logic for non-NER metrics
    // Check metro data
    const hasMetroData = propertyAveragesDates.every((date) => {
      const metroAverage = propertyAveragesData?.metro?.[date];
      if (!metroAverage) return false;
      const value = metroAverage[MetricTypeToKey[metricKey]];

      // For percentages (like concessions), 0 is a valid value
      if (metricKey.includes("concessionsPercent")) {
        return value !== undefined && value !== null;
      }
      // For rent metrics, 0 usually means no activity, so exclude it
      return value !== undefined && value !== null && value !== 0;
    });

    // Check property data
    const hasPropertyData =
      propertyAveragesData?.properties &&
      Object.values(propertyAveragesData.properties).some((propertyData) => {
        return propertyAveragesDates.every((date) => {
          const propertyAverage = propertyData.averages[date];
          if (!propertyAverage) return false;
          const value = propertyAverage[MetricTypeToKey[metricKey]];
          return value !== undefined && value !== null && value !== 0;
        });
      });

    return hasMetroData || hasPropertyData;
  };

  const hasDataForDemand = (demandKey: string) => {
    if (!propertyAveragesData || Object.keys(propertyAveragesData.metro)?.length < 1) return false;
    const hasData = propertyAveragesDates.every((date) => {
      const metroAverage = propertyAveragesData?.metro?.[date];
      if (!metroAverage) return false;
      const value = metroAverage[DemandTypeToKey[demandKey]];
      // For demand metrics, 0 usually means no activity, so exclude it
      return value !== undefined && value !== null && value !== 0;
    });
    return hasData;
  };

  const baseMetricOptions = [
    { value: "Avg Asking Rent", label: "Avg Asking Rent" },
    { value: "Median Asking Rent", label: "Median Asking Rent" },
    { value: "Avg Asking Rent PSF", label: "Avg Asking Rent PSF" },
    { value: "Median Asking Rent PSF", label: "Median Asking Rent PSF" },
    { value: "Concessions %", label: "Concessions % of Asking Rent" },
  ] as { value: MetricType; label: string }[];

  const nerMetricOptions = [
    { value: "Avg NER", label: "Avg NER" },
    { value: "Median NER", label: "Median NER" },
    { value: "Avg NER PSF", label: "Avg NER PSF" },
    { value: "Median NER PSF", label: "Median NER PSF" },
  ] as { value: MetricType; label: string; key: string }[];

  // Only include NER options if they have data
  const availableNEROptions = nerMetricOptions.filter((option) => hasDataForMetric(option.value));
  const allMetricOptions = [...baseMetricOptions, ...availableNEROptions];

  const allDemandOptions = [
    { value: "Demand", label: "Demand" },
    { value: "Virtual Tours", label: "Virtual Tours" },
    { value: "Self-Guided Tours", label: "Self-Guided Tours" },
  ] as { value: DemandType; label: string }[];

  // Filter options based on available data
  const metricOptions = allMetricOptions.filter((option) => hasDataForMetric(option.value));
  const demandOptions = allDemandOptions.filter((option) => hasDataForDemand(option.value));

  // Auto-adjust selection if current option is not available
  React.useEffect(() => {
    if (locationSelected) {
      // Check if current metric is still available
      const currentMetricAvailable = metricOptions.some((option) => option.value === selectedMetric);
      if (!currentMetricAvailable && metricOptions.length > 0) {
        setSelectedMetric(metricOptions[0].value as MetricType);
      }

      // Check if current demand is still available
      const currentDemandAvailable = demandOptions.some((option) => option.value === selectedDemand);
      if (!currentDemandAvailable && demandOptions.length > 0) {
        setSelectedDemand(demandOptions[0].value as DemandType);
      }
    }
  }, [locationSelected, metricOptions, demandOptions, selectedMetric, selectedDemand]);

  // Show indicator when no data is available
  const noMetricData = metricOptions.length === 0;
  const noDemandData = demandOptions.length === 0;

  return (
    <PeekPaper sx={{ p: 3 }}>
      <PeekStack direction="column" spacing={2} justifyContent="space-between">
        <PeekTypography color="text.primary" variant="h6">
          Market Data Analysis
        </PeekTypography>
      </PeekStack>
      <PeekStack
        direction="column"
        spacing={2}
        justifyContent="center"
        alignItems="center"
        minHeight={300}
        sx={{ width: "100%" }}
      >
        {(!locationSelected && (
          <NoDataFound message="Please select a metro area to view comparative market data and trends" />
        )) || (
          <>
            {propertyAveragesLoading && <PeekLoader />}
            {!propertyAveragesLoading && propertyAveragesError && (
              <NoDataFound error message="Could not load property averages data. Please try again later." />
            )}
            {!propertyAveragesLoading && !propertyAveragesError && (
              <>
                {noMetricData && noDemandData ? (
                  <NoDataFound message="No data available for selected properties" />
                ) : (
                  <PeekStack direction="column" spacing={2} sx={{ width: "100%" }}>
                    <PeekStack direction="row" spacing={2} alignItems="center" sx={{ justifyContent: "space-between" }}>
                      {!noMetricData && (
                        <PeekMetricSelector
                          selectedMetric={selectedMetric}
                          setSelectedMetric={(value) => setSelectedMetric(value as MetricType)}
                          metricOptions={metricOptions}
                          borderLeftStyle="solid"
                        />
                      )}
                      {!noDemandData && (
                        <PeekMetricSelector
                          selectedMetric={selectedDemand}
                          setSelectedMetric={(value) => setSelectedDemand(value as DemandType)}
                          metricOptions={demandOptions}
                          borderLeftStyle="dashed"
                        />
                      )}
                    </PeekStack>
                    <ResponsiveContainer width="100%" height={400}>
                      <LineChart data={marketData?.data}>
                        <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.grey[300]} />

                        <XAxis dataKey="date" axisLine={true} tickLine={true} tick={{ fontSize: 12 }} />

                        <YAxis
                          domain={[marketData.minMetricValue, marketData.maxMetricValue]}
                          yAxisId="left"
                          axisLine={true}
                          tickLine={true}
                          tick={{ fontSize: 12 }}
                          label={{
                            value: selectedMetric,
                            angle: -90,
                            position: "insideLeft",
                            style: {
                              textAnchor: "middle",
                              fontSize: 12,
                              fill: theme.palette.secondary.main,
                              fontWeight: theme.typography.fontWeightBold,
                            },
                          }}
                        />

                        <YAxis
                          domain={[marketData.minDemandValue, marketData.maxDemandValue]}
                          yAxisId="right"
                          orientation="right"
                          axisLine={true}
                          tickLine={true}
                          tick={{ fontSize: 12 }}
                          label={{
                            value: selectedDemand,
                            angle: 90,
                            position: "insideRight",
                            style: {
                              textAnchor: "middle",
                              fontSize: 12,
                              fill: theme.palette.secondary.main,
                              fontWeight: theme.typography.fontWeightBold,
                            },
                          }}
                        />

                        <Tooltip content={<CustomTooltip />} />

                        {lineChartConfig.map((line, index) => (
                          <Line
                            key={index}
                            type="monotone"
                            dataKey={line.dataKey}
                            stroke={line.stroke}
                            strokeWidth={2}
                            strokeDasharray={line.strokeDasharray}
                            dot={false}
                            activeDot={{ r: 4, fill: line.stroke }}
                            name={line.name}
                            yAxisId={line.yAxisId}
                            legendType={line.name.includes("_demand") ? "none" : "line"}
                            connectNulls={false}
                          />
                        ))}
                      </LineChart>
                    </ResponsiveContainer>
                    <CustomLegends items={legendItems} />
                  </PeekStack>
                )}
              </>
            )}
          </>
        )}
      </PeekStack>
    </PeekPaper>
  );
};

export default PropertiesAverages;
