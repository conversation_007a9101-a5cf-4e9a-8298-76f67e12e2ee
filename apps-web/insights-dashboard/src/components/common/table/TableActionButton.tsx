import React from "react";
import {
  PeekIconButtonProps,
  PeekIconButton,
  PeekTooltip,
  PeekTypography,
  PeekBox,
} from "@piiqtechnologies/ui-components";

interface IconProps {
  height: number | string;
  width: number | string;
}

interface ActionButtonProps extends PeekIconButtonProps {
  Icon: React.FC<IconProps>;
  height?: number | string;
  width?: number | string;
}

const ActionButton: React.FC<ActionButtonProps> = ({ Icon, height, width, ...props }) => {
  return (
    <PeekIconButton sx={{ height: 25, width: 25, border: 0, bgcolor: "transparent" }} {...props}>
      <PeekTypography color={(!props.disabled && "primary") || "grey.200"} lineHeight={0}>
        <Icon height={height} width={width} />
      </PeekTypography>
    </PeekIconButton>
  );
};

export interface TableActionButtonProps extends ActionButtonProps {
  tooltip?: React.ReactNode;
}

export const TableActionButton: React.FC<TableActionButtonProps> = ({ tooltip, height = 16, width = 16, ...props }) => {
  if (tooltip) {
    return (
      <PeekTooltip placement="bottom" title={<>{tooltip}</>}>
        <PeekBox>
          <ActionButton height={height} width={width} {...props} />
        </PeekBox>
      </PeekTooltip>
    );
  }
  return <ActionButton {...props} />;
};

export default TableActionButton;
