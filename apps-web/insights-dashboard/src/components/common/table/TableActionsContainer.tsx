import { PeekStack } from "@piiqtechnologies/ui-components";
import React, { ComponentPropsWithoutRef } from "react";

export const TableActionsContainer: React.FC<ComponentPropsWithoutRef<typeof PeekStack>> = ({ children, ...props }) => {
  return (
    <PeekStack {...props} justifyContent="end" data-testid="table-actions" spacing={0.5} mr={2} direction="row">
      {children}
    </PeekStack>
  );
};

export default TableActionsContainer;
