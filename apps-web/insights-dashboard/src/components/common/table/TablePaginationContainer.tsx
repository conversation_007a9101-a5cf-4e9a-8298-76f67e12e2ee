import React from "react";
import { PeekStack, PeekTablePagination } from "@piiqtechnologies/ui-components";

export interface TablePaginationContainerTypes {
  totalCount: number;
  onPageChange: (_event: any, page: number) => void;
  page: number;
  rowsPerPage: number;
}

export const TablePaginationContainer = ({
  totalCount,
  onPageChange,
  page,
  rowsPerPage,
}: TablePaginationContainerTypes) => {
  if (totalCount > rowsPerPage) {
    return (
      <PeekStack pt={1.5} direction="row" justifyContent="center">
        <PeekTablePagination count={Math.ceil(totalCount / rowsPerPage)} page={page} onChange={onPageChange} />
      </PeekStack>
    );
  }

  return null;
};

export default TablePaginationContainer;
