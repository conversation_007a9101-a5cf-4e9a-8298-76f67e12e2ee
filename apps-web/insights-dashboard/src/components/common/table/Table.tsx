import { Info } from "@piiqtechnologies/svg-icons";
import {
  PeekBox,
  PeekCheckbox,
  PeekStack,
  PeekTable,
  PeekTableBody,
  PeekTableCell,
  PeekTableContainer,
  PeekTableHeadFilter,
  PeekTableSkeletonLoader,
  PeekTableSortLabel,
  PeekTooltip,
  PeekTypography,
} from "@piiqtechnologies/ui-components";
import { useRefetch } from "@piiqtechnologies/ui-lib";
import React, { useEffect, useMemo, useState } from "react";
import { useIntl } from "react-intl";
import NoDataFound from "../no-data-found";
import { ArrowDrop } from "./ArrowDrop";
import TablePaginationContainer from "./TablePaginationContainer";
import { TableRow } from "./TableRow";
import { ColumnsWithCompiledLabels, Identifiable, TableColumnDefinition, TableProps, TableState } from "./types";

const DEFAULT_ROW_TRANSFORM: TableProps<any>["rowTransform"] = (row) => row;

interface EnhancedTableProps {
  rowCount: number;
  numSelected: number;
  onSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

function SelectableTableHead(props: EnhancedTableProps) {
  const { onSelectAllClick, numSelected, rowCount } = props;

  return (
    <PeekTableCell padding="checkbox">
      <PeekCheckbox
        color="secondary"
        indeterminate={numSelected > 0 && numSelected < rowCount}
        checked={rowCount > 0 && numSelected === rowCount}
        onChange={onSelectAllClick}
        inputProps={{
          "aria-label": "Select all",
        }}
        sx={{ paddingLeft: "4px !important" }}
      />
    </PeekTableCell>
  );
}

export function Table<T extends Identifiable>({
  columns: _columns,
  api,
  notFoundMessage = "Data not found",
  apiConfig = {},
  rowTransform = DEFAULT_ROW_TRANSFORM,
  actions,
  actionsCover,
  sortPredicate,
  tableProps,
  onClick,
  onOrderChange,
  onFiltersChange,
  onPageChange,
  onSelectionChange,
  rowSelectedContent,
  rowExpandedContent,
  expandButton,
  filterPredicate,
}: TableProps<T> & {
  rowSelectedContent?: (row: T) => React.JSX.Element;
  rowExpandedContent?: (row: T) => React.JSX.Element;
  expandButton?: (isOpen: boolean) => React.JSX.Element;
}) {
  const intl = useIntl();
  const { refetch } = useRefetch();
  const [order, setOrder] = useState<"asc" | "desc">("desc");
  const [orderBy, setOrderBy] = useState<string>();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<Record<string, Array<string>>>({});
  const [filteredData, setFilteredData] = useState<T[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<T[]>([]);

  const [fetchDataApi, apiResponse, isLoading, error] = api;

  // Protect API fetcher from accidental re-renders
  const fetchData = useMemo(() => {
    return fetchDataApi;
  }, []);

  const serverSidePagination = useMemo(() => {
    return apiConfig.serverSidePagination ?? true;
  }, [apiConfig]);

  const serverSideSorting = useMemo(() => {
    return apiConfig.serverSideSorting ?? true;
  }, [apiConfig]);

  const rowsPerPage = useMemo(() => {
    return apiConfig.rowsPerPage || 20;
  }, [apiConfig]);

  const tableState = useMemo(() => {
    return {
      filters,
      order,
      orderBy,
      page,
      selectedRows,
    } as TableState<T>;
  }, [filters, order, orderBy, selectedRows, serverSidePagination && page]);

  useEffect(() => {
    onOrderChange?.(tableState);
  }, [order, orderBy]);

  useEffect(() => {
    onFiltersChange?.(tableState);
  }, [filters]);

  useEffect(() => {
    onPageChange?.(tableState);
  }, [page]);

  useEffect(() => {
    onSelectionChange?.(tableState);
  }, [selectedRows]);

  useEffect(() => {
    setSelectedRows?.([]);
  }, [apiResponse]);

  // Rows after mapping, filtering, and sorting
  const rows = useMemo(() => {
    if (!apiResponse?.data) return [];

    let transformedData = apiResponse?.data.map((r) => rowTransform(r, tableState)).filter(Boolean);
    if (filterPredicate) {
      transformedData = transformedData.filter((row) => filterPredicate(row, filters));
    }
    if (!sortPredicate) return transformedData;
    if (!orderBy) return transformedData;

    return transformedData.sort((a, b) => sortPredicate(a, b, tableState));
  }, [apiResponse, rowTransform, sortPredicate, tableState, rowsPerPage, filterPredicate]);

  // Represents the rows that will be displayed, if serverside pagination is enabled it will display all rows of that page.
  // If serverside pagination is disabled, it will slice the rows based on the page and rowsPerPage
  const cappedRows = useMemo(() => {
    if (serverSidePagination) return rows;
    return rows.slice((page - 1) * rowsPerPage, page * rowsPerPage);
  }, [rows, page, rowsPerPage, serverSidePagination]);

  // Represents the total number of rows to be displayed in the page.
  // It can be affected by rowsCap or in case of server side pagination, the total count from the API response
  const totalCount = useMemo(() => {
    if (!serverSidePagination) return rows?.length || 0;
    return apiResponse?.totalCount ?? 0;
  }, [apiResponse, rowTransform, tableState]);

  useEffect(() => {
    if (serverSidePagination) return;
    if (rows?.length === apiResponse?.totalCount) return;
    if (!rowTransform) return;

    setPage(1);
  }, [apiResponse, rowTransform, tableState]);

  // Initial render, only fetch data when server side is disabled.
  useEffect(() => {
    if (serverSidePagination || serverSideSorting) return;
    const params = (typeof apiConfig.params === "function" ? apiConfig.params(tableState) : apiConfig.params) || {};
    if (serverSidePagination) {
      params[apiConfig.limitKey || "limit"] = rowsPerPage;
      params[apiConfig.offsetKey || "offset"] = rowsPerPage * (page - 1);
      params[apiConfig.orderByKey || "orderBy"] = orderBy;
      params[apiConfig.orderKey || "order"] = order;
    }
    if (fetchData) fetchData(params);
  }, [refetch, fetchData, apiConfig.params, serverSideSorting && tableState]);

  // Refetch data on page change, only when server side pagination is enabled
  useEffect(() => {
    if (!serverSidePagination && !serverSideSorting) return;
    const params = (typeof apiConfig.params === "function" ? apiConfig.params(tableState) : apiConfig.params) || {};
    params[apiConfig.limitKey || "limit"] = rowsPerPage;
    params[apiConfig.offsetKey || "offset"] = rowsPerPage * (page - 1);
    params[apiConfig.orderByKey || "orderBy"] = orderBy;
    params[apiConfig.orderKey || "order"] = order;
    if (fetchData) fetchData(params);
  }, [
    refetch,
    apiConfig.params,
    tableState.filters,
    tableState.order,
    tableState.orderBy,
    tableState.page,
    rowsPerPage,
  ]);

  const handleFilterChange = (column: TableColumnDefinition<T>) => (_filters: Array<string>) => {
    setFilters((prev) => ({ ...prev, [column.id]: _filters }));
    setPage(1);
  };

  const handleSelectRow = (row: T, checked: boolean) => {
    if (checked) {
      setSelectedRows([row, ...selectedRows]);
      return;
    }

    setSelectedRows(selectedRows.filter((r) => r._id !== row._id));
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (selectedRows.length > 0) {
      setSelectedRows([]);
      return;
    }

    if (event.target.checked) {
      setSelectedRows(rows);
      return;
    }

    setSelectedRows([]);
  };

  // Columns with all labels compiled to string.
  const columns: ColumnsWithCompiledLabels<T> = useMemo(() => {
    return _columns.map((column) => ({
      ...column,
      label: typeof column.label === "string" ? column.label : intl.formatMessage(column.label),
      filterOptions: column.filterOptions?.map((filter) => ({
        ...filter,
        label: typeof filter.label === "string" ? filter.label : intl.formatMessage(filter.label),
      })),
    }));
  }, [_columns]);

  const handleOrderChange = (columnId: string) => {
    setOrder((prev) => (prev === "asc" ? "desc" : "asc"));
    setOrderBy(columnId);
  };

  const handleChangePage = (_event: React.MouseEvent<HTMLButtonElement, MouseEvent>, newPage: number) => {
    setPage(newPage);
  };

  return (
    <PeekTableContainer>
      <PeekTable {...tableProps} stickyHeader>
        {onSelectionChange && (
          <SelectableTableHead
            rowCount={rows.length}
            numSelected={selectedRows.length}
            onSelectAllClick={handleSelectAllClick}
          />
        )}
        {columns.map((column) => (
          <PeekTableCell {...column.columnCellProps} key={column.id}>
            <PeekStack direction="row" spacing={0.5} alignItems="center">
              {column.sortable ? (
                <PeekTableSortLabel
                  sx={{ fontWeight: "fontWeightMedium" }}
                  active={orderBy === column.id}
                  direction={orderBy === column.id ? order : "asc"}
                  onClick={() => handleOrderChange(column.id)}
                  IconComponent={() => (
                    <ArrowDrop active={orderBy === column.id} direction={orderBy === column.id ? order : "desc"} />
                  )}
                >
                  <PeekTypography fontWeight="fontWeightMedium">{column.label}</PeekTypography>
                </PeekTableSortLabel>
              ) : (
                <PeekStack direction={"row"} spacing={0.6}>
                  <PeekBox>
                    <PeekTypography fontWeight="fontWeightMedium" whiteSpace={"nowrap"}>
                      {column.label}
                    </PeekTypography>
                  </PeekBox>
                  {column.infoText && (
                    <PeekTooltip title={<PeekTypography>{column.infoText}</PeekTypography>} placement="top">
                      <PeekTypography variant="h3">
                        <Info />
                      </PeekTypography>
                    </PeekTooltip>
                  )}
                </PeekStack>
              )}
              {column.filterOptions && (
                <PeekTableHeadFilter
                  onFilterChange={handleFilterChange(column)}
                  rangeFilter={column.rangeFilter}
                  filters={column.filterOptions}
                />
              )}
            </PeekStack>
          </PeekTableCell>
        ))}
        <PeekTableBody>
          {!!cappedRows?.length &&
            cappedRows.map((row) => {
              return (
                <TableRow
                  key={row._id}
                  row={row}
                  columns={columns}
                  onClick={onClick}
                  isSelected={selectedRows.some((r) => r._id === row._id)}
                  onSelect={onSelectionChange && ((checked) => handleSelectRow(row, checked))}
                  actionsCover={actionsCover}
                  actions={actions}
                  selectedContent={rowSelectedContent}
                  expandededContent={rowExpandedContent}
                  expandButton={expandButton}
                />
              );
            })}
        </PeekTableBody>
      </PeekTable>

      {error && <NoDataFound message={error.message} error />}
      {isLoading && <PeekTableSkeletonLoader numberOfLines={5} />}
      {!isLoading && !error && !rows?.length && (
        <NoDataFound
          message={typeof notFoundMessage === "string" ? notFoundMessage : intl.formatMessage(notFoundMessage)}
        />
      )}

      <TablePaginationContainer
        totalCount={totalCount}
        onPageChange={handleChangePage}
        page={page}
        rowsPerPage={rowsPerPage}
      />
    </PeekTableContainer>
  );
}
