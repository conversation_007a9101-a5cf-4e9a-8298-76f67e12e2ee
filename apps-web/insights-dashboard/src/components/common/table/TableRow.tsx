import {
  PeekBox,
  PeekCheckbox,
  PeekCollapse,
  PeekDivider,
  PeekStack,
  PeekTableCell,
  PeekTableRow,
  useTheme,
} from "@piiqtechnologies/ui-components";
import getProperty from "lodash/get";
import React, { useCallback, useMemo, useState } from "react";
import TableActionsContainer from "./TableActionsContainer";
import { Identifiable, TableProps } from "./types";

interface ActionsContainerProps {
  id: string;
  display: boolean;
  actions: React.ReactNode[];
}

const ActionsContainer = ({ display, actions, id }: ActionsContainerProps) => {
  return (
    <TableActionsContainer display={display ? "flex" : "none"}>
      {actions.map((action, i) => {
        return (
          <React.Fragment key={`${id}-action-${i}`}>
            {i !== 0 && <PeekDivider orientation="vertical" flexItem={true} />}
            {action}
          </React.Fragment>
        );
      })}
    </TableActionsContainer>
  );
};

export function TableRow<T extends Identifiable>({
  row,
  columns,
  actions,
  actionsCover,
  onClick,
  isSelected,
  onSelect,
  selectedContent: SelectedRowContent,
  expandededContent: ExpandedRowContent,
  expandButton,
}: Pick<TableProps<T>, "actions" | "actionsCover" | "columns" | "onClick"> & {
  row: T;
  isSelected: boolean;
  onSelect?: (checked: boolean) => void;
  selectedContent?: (row: T) => React.JSX.Element;
  expandededContent?: (row: T) => React.JSX.Element;
  expandButton?: (isOpen: boolean) => React.JSX.Element;
}) {
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [actionsVisible, setActionsVisible] = useState(false);

  const actionsCoverComponents = useMemo(() => {
    if (!actionsCover) return [];
    return actionsCover(row);
  }, [actionsCover]);

  const actionsComponents = useMemo(() => {
    if (!actions) return [];
    return actions(row);
  }, [actions]);

  const handleMouseEnter = useCallback(() => {
    setActionsVisible(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setActionsVisible(false);
  }, []);

  const handleClickRow = useCallback(() => {
    onClick?.(row);
  }, []);

  return (
    <>
      <PeekTableRow
        key={row._id}
        id={row._id}
        onClick={!!onClick ? handleClickRow : undefined}
        role="checkbox"
        aria-checked={isSelected}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        sx={{
          bgcolor: theme.palette.grey[50],
          ...(!onSelect && {
            "&:hover": { bgcolor: theme.palette.grey[100], cursor: onClick && "pointer" },
          }),
        }}
      >
        {onSelect && (
          <PeekTableCell padding="checkbox">
            <PeekCheckbox
              color="secondary"
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
              sx={{ padding: "4px" }}
            />
          </PeekTableCell>
        )}

        {columns.map((column) => {
          return (
            <PeekTableCell onClick={handleClickRow} key={`${row._id}${column.id}`} {...column.rowCellProps}>
              <PeekBox onClick={(event) => event.stopPropagation()}>
                {(column.render ? column.render(row) : getProperty(row, column.accessor)) ?? "-"}
              </PeekBox>
            </PeekTableCell>
          );
        })}

        {!!ExpandedRowContent && !!expandButton && (
          <PeekTableCell key={"expand-button"}>
            <PeekBox
              onClick={(event) => {
                event.stopPropagation();
                setIsOpen(!isOpen);
              }}
            >
              {expandButton(isOpen)}
            </PeekBox>
          </PeekTableCell>
        )}

        {actions || actionsCover ? (
          <PeekTableCell align="right" width={theme.spacing(15)} height={theme.spacing(3)}>
            {actions && <ActionsContainer display={actionsVisible} actions={actionsComponents} id={row._id} />}
            {actionsCover && (
              <ActionsContainer display={!actionsVisible} actions={actionsCoverComponents} id={row._id} />
            )}
          </PeekTableCell>
        ) : (
          ""
        )}
      </PeekTableRow>

      {!!ExpandedRowContent && (
        <PeekTableRow sx={{ transform: "translateY(-6px)", display: isOpen ? "table-row" : "none" }}>
          <PeekTableCell colSpan={12}>
            <PeekCollapse in={isOpen} timeout="auto" unmountOnExit>
              <ExpandedRowContent {...row} />
            </PeekCollapse>
          </PeekTableCell>
        </PeekTableRow>
      )}

      {isSelected && !!SelectedRowContent && (
        <PeekTableRow sx={{ transform: "translateY(-16px)" }}>
          <PeekTableCell colSpan={12}>
            <PeekStack width={"100%"}>
              <PeekDivider light orientation={"horizontal"} sx={{ width: "calc(100% - 14px)" }} />
              <SelectedRowContent {...row} />
            </PeekStack>
          </PeekTableCell>
        </PeekTableRow>
      )}
    </>
  );
}
