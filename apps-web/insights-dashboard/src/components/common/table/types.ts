import { PeekTable, PeekTableCellProps } from "@piiqtechnologies/ui-components";
import { PropsWithoutRef } from "react";
import { MessageDescriptor } from "react-intl";

type PathsImpl<T, Key extends keyof T> = Key extends string
  ? T[Key] extends Record<string, any>
    ? `${Key}.${Paths<T[Key]>}`
    : Key
  : never;

type Label = string | MessageDescriptor;

export type ColumnsWithCompiledLabels<T> = Array<
  Omit<TableColumnDefinition<T>, "label" | "filterOptions"> & {
    label: string;
    labelIcon?: React.ReactNode;
    filterOptions?: Array<{ id: string; label: string }>;
    infoText?: string;
  }
>;

export type FilterOptions = {
  id: string;
  label: Label;
};

export type TableEvents = "selectionChange" | "pageChange" | "orderChange" | "filtersChange";

export type TableEventHandlers<T extends Identifiable> = {
  [key in `on${Capitalize<TableEvents>}`]?: (currentState: TableState<T>) => void;
};

export type Paths<T> = PathsImpl<T, keyof T>;

export type Identifiable = { _id: string };

type TableBaseColumnDefinition = {
  /**
   * Unique identifier for the column.
   */
  id: string;

  /**
   * Column label.
   * Can be a string or a message descriptor from Intl.
   */
  label: Label;

  /**
   * If the column is sortable.
   */
  sortable?: boolean;

  /**
   * Cell Props to be passed to the PeekTableCell component.
   */
  rowCellProps?: PeekTableCellProps;

  /**
   * Cell Props to be passed to the PeekTableCell component.
   */
  columnCellProps?: PeekTableCellProps;

  /**
   * Filters to be applied to the column.
   * @param data All data available on that page.
   */
  filterOptions?: Array<FilterOptions>;

  /**
   * If the column should be used for range filtering.
   * @default false
   */
  rangeFilter?: boolean;
};

export type TableColumnDefinition<T> = TableBaseColumnDefinition & {
  /**
   *
   * A function that returns a react component or node to be rendered in the cell.
   * If passed, the property accessor will be ignored.
   * @example
   * ```ts
   * {
   *  render: (row) => <span>{row.name}</span>
   * }
   * ```
   * @param row The row data of type {T}
   * @returns A react component or node
   */
  render?: (row: T) => React.ReactNode;
  /**
   * A string that represents the path to the property to be displayed in the cell.
   * Only necessary if render is not passed.
   * @example
   * ```ts
   * {
   *  accessor: "foo.bar"
   * }
   * ```
   * Wil display the value of row.foo.bar
   */
  accessor?: string;
};

type TableApiError = { message?: string };
type TableIsLoading = boolean;
type TableDataFetcher = (filters?: Record<string, any>) => Promise<void>;
type TableData<T> = { data: T[]; totalCount: number };

export type TableApi<T> = [TableDataFetcher?, TableData<T>?, TableIsLoading?, TableApiError?];

export type TableApiConfig = {
  /**
   * Params to be passed to the data fetcher.
   */
  params?: Record<string, any> | ((tableState: TableState) => Record<string, any>);

  /**
   * If the data is server side paginated.
   * @default true
   */
  serverSidePagination?: boolean;

  /**
   * Number of rows per page.
   * @default 20
   */
  rowsPerPage?: number;

  /**
   * Key to be used on params for offset in pagination.
   * @default "offset"
   */
  offsetKey?: string;

  /**
   * Key to be used on params for limit in pagination.
   * @default "limit"
   */
  limitKey?: string;

  /**
   * If the data is server side filtered.
   * @default true
   * @todo Implement server side filtering
   */
  serverSideFilters?: boolean;

  /**
   * Max number of rows to be shown.
   * @default gets value from apiResponse?.totalCount
   * @deprecated It was born deprecated. But we need to build a CRUD framework on the backend to stop using it.
   */
  rowsCap?: number;

  /**
   * If the data is server side sorted.
   */
  serverSideSorting?: boolean;

  /**
   * Key to be used on params for order in sorting.
   * @default "orderBy"
   */
  orderByKey?: string;

  /**
   * Key to be used on params for order in sorting.
   * @default "order"
   */
  orderKey?: string;
};

export type TableState<T extends Identifiable = Identifiable> = {
  /**
   * Current page.
   */
  page: number;

  /**
   * Current order.
   */
  order: "asc" | "desc";

  /**
   * Current orderBy.
   */
  orderBy: string;

  /**
   * Current filters.
   */
  filters: Record<string, Array<string>>;

  /**
   *
   */
  selectedRows: Array<T>;
};

export type TableProps<T extends Identifiable> = {
  /**
   * Table columns definition, each column will be displayed in the order they are defined.
   */
  columns: TableColumnDefinition<T>[];

  /**
   * Table data, an array with 4 elements.
   * The first element is a function that fetches the data.
   * The second element is the data itself.
   * The third element is a boolean that indicates if the data is loading.
   * The fourth element is an error object.
   */
  api: TableApi<T>;

  /**
   * Function that will transform each row shape, the render or accessor will be applied on transformed data
   * ! This function will be called on each render, so be careful with performance.
   * ! Return undefined if you want the given data not to be in the table. e.g. applying filters
   * @param row The row data of type {T}
   * @param tableState The current state of the table
   * @returns The transformed row data or {undefined} if you want the row not to be displayed
   *
   * !Avoid using this function. Instead do server side filtering and transforming.
   */
  rowTransform?: (row: T, tableState: TableState<T>) => T | undefined;

  /**
   * Function that will render the actions column. It will only be rendered when hovering the row.
   * @param row The row data of type {T}
   * @returns A react node
   */
  actions?: (row: T) => React.ReactNode[];

  /**
   * Function that will execute a row action. It will only be called when clicking the row.
   * @param row The row data of type {T}
   */
  onClick?: (row: T) => void;

  /**
   * Function that will render an actions cover column. It will only be rendered when not hovering the row.
   * @param row The row data of type {T}
   * @returns A react node
   */
  actionsCover?: (row: T) => React.ReactNode[];

  /**
   * Configuration related to fetching data from the API
   */
  apiConfig?: TableApiConfig;

  /**
   * Function that will be passed to sort the data.
   * @param a The first row data of type {T}
   * @param b The second row data of type {T}
   * @param tableState The current state of the table
   *
   * ! Avoid using this function. Instead do server side sorting.
   */
  sortPredicate?: (a: T, b: T, tableState: TableState<T>) => number;

  filterPredicate?: (row: T, filters: Record<string, Array<string>>) => boolean;

  /**
   * Message to be displayed when there is no data.
   */
  notFoundMessage?: Label;

  /**
   * Peek Table Props
   */
  tableProps?: PropsWithoutRef<typeof PeekTable>;
} & TableEventHandlers<T>;
