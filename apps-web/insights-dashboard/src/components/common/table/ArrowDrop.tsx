import { PeekTypography } from "@piiqtechnologies/ui-components";
import { ChevronDown, ChevronUp } from "@piiqtechnologies/svg-icons";
import React from "react";

interface ArrowDropProps {
  active: boolean;
  direction: "asc" | "desc";
}

export const ArrowDrop = (props: ArrowDropProps) => {
  return (
    <PeekTypography alignItems="end" color={(props.active && "secondary") || "primary"} sx={{ mx: 0.5, mt: 0.5 }}>
      {(props.direction === "desc" && <ChevronDown />) || <ChevronUp />}
    </PeekTypography>
  );
};
