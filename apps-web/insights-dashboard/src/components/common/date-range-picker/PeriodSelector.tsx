import { Peek<PERSON>utton, PeekButtonGroup, PeekStack, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import { useNotification } from "@piiqtechnologies/ui-lib";
import { add, differenceInDays, endOfWeek, isAfter, isBefore, startOfWeek, sub } from "date-fns";
import React, { useCallback, useMemo, useState } from "react";
import { useIntl } from "react-intl";
import CalendarPicker from "./CalendarPicker";
import { messages } from "./messages.i18n";

const MAXIMUM_PERIOD_IN_DAYS = 90;

const EARLIEST_DAY_POSSIBLE = 365;

export interface Period {
  startDate: Date;
  endDate: Date;
}

export const WEEK_PERIODS = [4, 8, 12];

interface PeriodSelectorProps {
  onChange: (period: Period) => void;
  value: Period;
  disabled?: boolean;
  hideCustom?: boolean;
}

export const PeriodSelector: React.FC<PeriodSelectorProps> = ({ onChange, value, disabled, hideCustom = true }) => {
  const theme = useTheme();
  const [dateChanged, setDateChanged] = useState<"startDate" | "endDate">();
  const { setNotification } = useNotification();
  const intl = useIntl();

  const numberOfWeeks = useMemo(() => {
    const days = differenceInDays(value.endDate, value.startDate) + 1;
    return Math.round(days / 7);
  }, [value]);

  const [showCalender, setShowCalender] = useState(false);

  const toggleShowCalender = () => {
    setShowCalender((showCalendar: boolean) => !showCalendar);
  };

  const handlePeriodChange = (period: number) => {
    const today = new Date();
    const endOfLastCompleteWeek = endOfWeek(sub(today, { days: 7 }), { weekStartsOn: 1 });
    const startOfPeriod = sub(endOfLastCompleteWeek, { days: period * 7 - 1 });
    const startOfCompleteWeek = startOfWeek(startOfPeriod, { weekStartsOn: 1 });

    onChange({ startDate: startOfCompleteWeek, endDate: endOfLastCompleteWeek });

    setDateChanged(undefined);
  };

  const isCustomPeriod = useMemo(() => {
    return dateChanged !== undefined;
  }, [dateChanged]);

  const handleCustomDateChanged = useCallback(
    (date: Date) => {
      const diffInDays = Math.max(differenceInDays(date, value.startDate), differenceInDays(value.endDate, date));

      let finalPeriod = { ...value, [dateChanged]: date };

      // Checks if the start date is greater than the end date and swaps them
      if (isAfter(finalPeriod.startDate, finalPeriod.endDate)) {
        finalPeriod = {
          startDate: finalPeriod.endDate,
          endDate: finalPeriod.startDate,
        };
        // Checks if the end date is less than the start date and swaps them
      } else if (isBefore(finalPeriod.endDate, finalPeriod.startDate)) {
        finalPeriod = {
          startDate: finalPeriod.endDate,
          endDate: finalPeriod.startDate,
        };
      }

      // If the difference in days is greater than the maximum period, adjust the period
      if (diffInDays > MAXIMUM_PERIOD_IN_DAYS) {
        if (dateChanged === "startDate") {
          finalPeriod = {
            startDate: finalPeriod.startDate,
            endDate: add(finalPeriod.startDate, { days: MAXIMUM_PERIOD_IN_DAYS }),
          };
        } else {
          finalPeriod = {
            startDate: sub(finalPeriod.endDate, { days: MAXIMUM_PERIOD_IN_DAYS }),
            endDate: finalPeriod.endDate,
          };
        }

        setNotification(
          intl.formatMessage(messages.periodLimitExceededMessage, {
            maxPeriod: MAXIMUM_PERIOD_IN_DAYS,
            dateType: dateChanged === "startDate" ? "end date" : "start date",
          }),
        );
      }

      onChange(finalPeriod);
    },
    [value, dateChanged],
  );

  const availablePeriods = WEEK_PERIODS;

  return (
    <PeekButtonGroup sx={{ position: "relative" }} disabled={disabled}>
      {/* PRE-DEFINED PERIODS */}
      {availablePeriods
        .filter((item) => typeof item === "number")
        .map((item) => {
          const isSelected = numberOfWeeks === item && !dateChanged;

          return (
            <PeekButton
              key={item}
              size="large"
              onClick={() => handlePeriodChange(item)}
              variant={isSelected ? "contained" : "outlined"}
              color={isSelected ? "primary" : "info"}
              sx={{
                height: theme.spacing(4),
              }}
            >
              {item}wk
            </PeekButton>
          );
        })}
      {!hideCustom && (
        <PeekButton
          key={"custom"}
          size="large"
          variant={isCustomPeriod ? "contained" : "outlined"}
          color={isCustomPeriod ? "primary" : "info"}
          sx={{
            height: theme.spacing(4),
            "&:hover": {
              backgroundColor: `${isCustomPeriod ? theme.palette.primary.main : "white"} !important`,
              borderColor: `${theme.palette.grey[100]} !important`,
              cursor: "default !important",
              color: `${isCustomPeriod ? "white" : theme.palette.text.primary} !important`,
            },
          }}
        >
          <PeekStack direction="row" spacing={1} alignItems="center">
            <CalendarPicker
              onClose={toggleShowCalender}
              onChange={(newDate: Date) => handleCustomDateChanged(newDate)}
              open={dateChanged === "startDate" && showCalender}
              value={value.startDate}
              minDate={sub(new Date(), { days: EARLIEST_DAY_POSSIBLE })}
              maxDate={sub(new Date(), { days: 1 })}
              onClick={() => {
                setDateChanged("startDate");
                toggleShowCalender();
              }}
            />

            <PeekTypography variant="h3">-</PeekTypography>

            <CalendarPicker
              onClose={toggleShowCalender}
              onChange={(newDate: Date) => handleCustomDateChanged(newDate)}
              open={dateChanged === "endDate" && showCalender}
              value={value.endDate}
              minDate={sub(new Date(), { days: EARLIEST_DAY_POSSIBLE })}
              maxDate={sub(new Date(), { days: 1 })}
              onClick={() => {
                setDateChanged("endDate");
                toggleShowCalender();
              }}
            />
          </PeekStack>
        </PeekButton>
      )}
    </PeekButtonGroup>
  );
};

export default PeriodSelector;
