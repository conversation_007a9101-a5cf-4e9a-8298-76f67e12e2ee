import { PeekCalendar, PeekTypography, theme } from "@piiqtechnologies/ui-components";
import { format } from "date-fns";
import React from "react";

const PopperProps = {
  disablePortal: true,
  sx: {
    width: "fit-content",
    position: "absolute !important",
    top: `44px !important`,
    right: `0 !important`,
    left: `unset !important`,
    ".MuiDayPicker-weekDayLabel": { color: "text.primary" },
    ".PrivatePickersSlideTransition-root": { minHeight: "200px" },
    ".PrivatePickersYear-yearButton.Mui-disabled": { color: "text.disabled" },
  },
};

export interface CalendarPickerProps {
  open: boolean;
  value: Date;
  minDate?: Date;
  maxDate?: Date;
  onChange: (date: Date) => void;
  onClick: () => void;
  onClose: () => void;
}

export const CalendarPicker: React.FC<CalendarPickerProps> = ({
  open,
  value,
  minDate,
  maxDate,
  onChange,
  onClick,
  onClose,
}) => {
  return (
    <PeekCalendar
      onClose={onClose}
      onChange={onChange}
      open={open}
      value={value}
      minDate={minDate}
      maxDate={maxDate}
      allowSameDateSelection
      renderInput={() => (
        <PeekTypography
          whiteSpace="nowrap"
          onClick={onClick}
          sx={{
            cursor: "pointer !important",
            ":hover": {
              color: theme.palette.secondary.main,
            },
          }}
        >
          {format(value, "MMM dd")}
        </PeekTypography>
      )}
      PopperProps={PopperProps}
    />
  );
};

export default CalendarPicker;
