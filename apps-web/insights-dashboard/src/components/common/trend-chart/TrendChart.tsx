import React from "react";
import { PeekBox, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";

interface TrendChartProps {
  data?: number[];
  trend?: "up" | "down";
  percentage?: string;
  color?: "green" | "red";
  width?: number;
  height?: number;
}

const TrendChart: React.FC<TrendChartProps> = ({
  data = [1, 3, 2, 4, 3, 5, 4, 6, 5, 7],
  trend = "up",
  percentage = "11%",
  color = "green",
  width = 80,
  height = 20,
}) => {
  const theme = useTheme();

  const strokeColor = color === "green" ? "#4CAF50" : "#F44336";

  // Normalize data to fit within the SVG height
  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  // Create SVG path
  const pathData = data
    .map((value, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      return `${index === 0 ? "M" : "L"} ${x} ${y}`;
    })
    .join(" ");

  return (
    <PeekBox sx={{ display: "flex", alignItems: "center", gap: 1 }}>
      <svg width={width} height={height} style={{ overflow: "visible" }}>
        <path d={pathData} fill="none" stroke={strokeColor} strokeWidth="1.5" vectorEffect="non-scaling-stroke" />
        {/* Add dots at data points */}
        {data.map((value, index) => {
          const x = (index / (data.length - 1)) * width;
          const y = height - ((value - min) / range) * height;
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="1.5"
              fill={strokeColor}
              opacity={index === 0 || index === data.length - 1 ? 1 : 0.6}
            />
          );
        })}
      </svg>
      <PeekTypography
        variant="caption"
        sx={{
          color: strokeColor,
          fontSize: "10px",
          fontWeight: 500,
          marginLeft: 0.5,
        }}
      >
        {trend === "up" ? "+" : "-"}
        {percentage}
      </PeekTypography>
    </PeekBox>
  );
};

export default TrendChart;
