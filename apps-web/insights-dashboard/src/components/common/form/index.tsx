import React, { useEffect } from "react";
import { Form as FormikForm, useFormikContext } from "formik";
import { PeekButton, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import { FormFieldType } from "./FormConfig";
import Fields from "./FormFields";

interface FormProps {
  error?: Error;
  errorText?: string;
  formFields: FormFieldType[];
  formSpacing?: number;
  submitText: string;
}

const Form: React.FC<FormProps> = ({ error, formFields, formSpacing = 1.5, submitText, errorText }) => {
  const { resetForm } = useFormikContext();

  useEffect(() => {
    if (error) {
      resetForm();
    }
  }, [error]);

  return (
    <FormikForm>
      <PeekStack direction="column" spacing={formSpacing}>
        <Fields fields={formFields} />
        <PeekStack direction="column" spacing={1} alignItems="center">
          {error && (
            <PeekTypography variant="body1" color="error">
              {errorText || error.message}
            </PeekTypography>
          )}
          <PeekButton fullWidth type="submit" variant="contained">
            {submitText}
          </PeekButton>
        </PeekStack>
      </PeekStack>
    </FormikForm>
  );
};

export default Form;
