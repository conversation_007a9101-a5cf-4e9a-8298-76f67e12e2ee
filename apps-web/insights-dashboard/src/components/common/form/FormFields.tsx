import { PeekGrid, PeekInput, PeekStack } from "@piiqtechnologies/ui-components";
import { Field } from "formik";
import React from "react";
import { FormFieldType } from "./FormConfig";

export interface FormFieldsProps extends Record<string, any> {
  fields: FormFieldType[];
}

const FormFields: React.FC<FormFieldsProps> = ({ fields, ...props }) => {
  return (
    <PeekStack direction="column" justifyContent="space-between" textAlign="left">
      {fields.map(({ id, label, placeholder, required, type, component: FormComponent, multiline, rows }) => {
        return (
          <Field
            id={id}
            key={id}
            name={id}
            label={label}
            type={type && type}
            placeholder={placeholder}
            required={required}
            component={FormComponent || PeekInput}
            multiline={multiline}
            rows={rows}
            {...props}
          />
        );
      })}
    </PeekStack>
  );
};

export interface MultiColumnFormFieldsProps extends Record<string, any> {
  fields: Record<string, FormFieldType[]>;
  rowSpacing?: number;
  alignItems?: string;
}

export const MultiColumnFormFields = ({ fields, rowSpacing, alignItems, ...props }: MultiColumnFormFieldsProps) => {
  return (
    <PeekGrid container direction="column" rowSpacing={rowSpacing}>
      {/* CONTENT FIELDS */}
      {Object.entries(fields).map(([rowKey, rowFields]) => (
        <PeekGrid
          item
          container
          key={rowKey}
          alignItems={alignItems || "flex-start"}
          direction="row"
          columnSpacing={1.5}
        >
          {rowFields.map(
            ({
              id,
              label,
              placeholder,
              required,
              type,
              component,
              columns,
              multiline,
              rows,
              fullWidth,
              style,
              ...fieldProps
            }) => {
              return (
                <PeekGrid style={style} key={id} item xs={columns}>
                  <Field
                    id={id}
                    name={id}
                    label={label}
                    type={type && type}
                    placeholder={placeholder}
                    required={required}
                    multiline={multiline}
                    rows={rows}
                    component={component || PeekInput}
                    fullWidth={fullWidth}
                    {...fieldProps}
                    {...props}
                  />
                </PeekGrid>
              );
            },
          )}
        </PeekGrid>
      ))}
    </PeekGrid>
  );
};

export default FormFields;
