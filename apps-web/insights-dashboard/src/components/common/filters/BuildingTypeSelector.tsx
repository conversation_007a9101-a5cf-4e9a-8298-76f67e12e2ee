import MultipleSelectorWithCount from "@common-components/multiple-selector-with-count";
import { useInsightsBuildingTypes } from "@piiqtechnologies/ui-lib";
import React, { useEffect, useMemo } from "react";
import { FilterFieldComponentProps } from "./FilterField";

interface BuildingTypeSelectorProps extends FilterFieldComponentProps {
  onChange: (value: string[]) => void;
  value: string[];
}

export const BuildingTypeSelector: React.FC<BuildingTypeSelectorProps> = ({ label, value, onChange, disabled }) => {
  const [getBuildingTypes, buildigTypes] = useInsightsBuildingTypes();

  useEffect(() => {
    getBuildingTypes();
  }, []);

  const options = useMemo(() => {
    return buildigTypes
      ? buildigTypes.map((bt: { name: string }) => ({
          label: bt.name,
          value: bt.name,
        }))
      : [];
  }, [buildigTypes]);

  return (
    <MultipleSelectorWithCount
      label={label}
      options={options}
      selected={Array.isArray(value) ? value : []}
      onSelect={onChange}
      disabled={disabled}
    />
  );
};
