import MultipleSelectorWithCount from "@common-components/multiple-selector-with-count";
import { InsightsSquareFoot } from "@piiqtechnologies/ui-lib";
import React, { useMemo } from "react";
import { FilterFieldComponentProps } from "./FilterField";

interface SFSelectorProps extends FilterFieldComponentProps {
  onChange: (value: InsightsSquareFoot[]) => void;
  value: InsightsSquareFoot[];
}

const SF_TYPES: InsightsSquareFoot[] = [
  { min: 300, max: 500 },
  { min: 501, max: 1000 },
  { min: 1001, max: 5000 },
];

export const SFSelector: React.FC<SFSelectorProps> = ({ label, value, onChange, disabled }) => {
  const options = useMemo(() => {
    return SF_TYPES.map((sf) => ({
      label: `${sf.min} - ${sf.max}`,
      value: `${sf.min}-${sf.max}`,
    }));
  }, []);

  const onSFChange = (selectedSF: string[]) => {
    if (!Array.isArray(selectedSF)) return;
    const formattedSF = selectedSF.map((sf) => {
      const [min, max] = sf.split("-");
      return { min: parseInt(min, 10), max: parseInt(max, 10) };
    });
    onChange(formattedSF);
  };

  const selectedSF = useMemo(() => {
    if (!Array.isArray(value)) return [];
    return value.map((sf) => `${sf.min}-${sf.max}`);
  }, [value]);

  return (
    <MultipleSelectorWithCount
      label={label}
      options={options}
      selected={selectedSF}
      onSelect={onSFChange}
      disabled={disabled}
    />
  );
};
