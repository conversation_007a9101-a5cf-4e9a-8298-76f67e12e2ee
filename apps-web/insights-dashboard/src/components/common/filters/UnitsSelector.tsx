import { PeekSelect, PeekSelectOption } from "@piiqtechnologies/ui-components";
import { FilterFieldComponentProps } from "./FilterField";
import React from "react";

interface UnitsSelectorProps extends FilterFieldComponentProps {
  onChange: (value: string) => void;
  value: string;
}

const NO_OF_UNITS_OPTIONS = [
  { value: "", label: "Select no of units" },
  { value: "50", label: ">50 units" },
  { value: "200", label: ">200 units" },
  { value: "300", label: ">300 units" },
];

const UnitsSelector: React.FC<UnitsSelectorProps> = ({ label, value, onChange, disabled }) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.value);
  };

  return (
    <PeekSelect
      size="small"
      sx={{ py: 0.3 }}
      fullWidth
      displayEmpty
      renderValue={(value) => (value ? `${label} >${value}` : label)}
      value={value}
      disabled={disabled}
      onChange={handleChange}
    >
      {NO_OF_UNITS_OPTIONS.map((option) => (
        <PeekSelectOption key={option.value} value={option.value}>
          {option.label}
        </PeekSelectOption>
      ))}
    </PeekSelect>
  );
};

export default UnitsSelector;
