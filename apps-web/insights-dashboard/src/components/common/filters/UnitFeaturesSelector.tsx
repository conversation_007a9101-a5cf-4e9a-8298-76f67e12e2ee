import MultipleSelectorWithCount from "@common-components/multiple-selector-with-count";
import React, { useEffect, useMemo } from "react";
import { FilterFieldComponentProps } from "./FilterField";
import { useInsightsUnitsAmenities } from "@piiqtechnologies/ui-lib";

interface UnitFeaturesSelectorProps extends FilterFieldComponentProps {
  onChange: (value: string[]) => void;
  value: string[];
}
export const UnitFeaturesSelector: React.FC<UnitFeaturesSelectorProps> = ({ label, value, onChange, disabled }) => {
  const [getUnitsAmenities, unitsAmenities] = useInsightsUnitsAmenities();

  useEffect(() => {
    getUnitsAmenities();
  }, []);

  const options = useMemo(() => {
    return unitsAmenities
      ? unitsAmenities.map((a) => {
          return {
            label: a.name,
            value: a.name,
          };
        })
      : [];
  }, [unitsAmenities]);

  return (
    <MultipleSelectorWithCount
      label={label}
      options={options}
      selected={Array.isArray(value) ? value : []}
      onSelect={onChange}
      disabled={disabled}
    />
  );
};
