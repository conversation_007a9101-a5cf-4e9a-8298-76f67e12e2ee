import MultipleSelectorWithCount from "@common-components/multiple-selector-with-count";
import React from "react";
import { FilterFieldComponentProps } from "./FilterField";

interface LayoutSelectorProps extends FilterFieldComponentProps {
  onChange: (value: string[]) => void;
  value: string[];
}

const LAYOUT_TYPES = [
  { label: "Studio", value: "0" },
  { label: "1 Bed", value: "1" },
  { label: "2 Bed", value: "2" },
];

export const LayoutSelector: React.FC<LayoutSelectorProps> = ({ label, value, onChange, disabled }) => {
  return (
    <MultipleSelectorWithCount
      label={label}
      options={LAYOUT_TYPES}
      selected={Array.isArray(value) ? value : []}
      onSelect={onChange}
      disabled={disabled}
    />
  );
};
