import React, { FC } from "react";

export interface FilterFieldComponentProps {
  label?: string;
  value: string | number | string[] | number[] | { [key: string]: any };
  onChange: (value: string | number | string[] | number[] | { [key: string]: any }) => void;
  disabled?: boolean;
}

interface FilterFieldProps {
  name: string;
  label: string;
  value: string | number | string[] | number[] | { [key: string]: any };
  onFilterChange: (name: string, value: string | number | string[] | number[] | { [key: string]: any }) => void;
  FilterComponent: FC<FilterFieldComponentProps>;
  disabled?: boolean;
}

export const FilterField: React.FC<FilterFieldProps> = ({
  name,
  label,
  value,
  onFilterChange,
  FilterComponent,
  disabled,
}) => {
  const handleFilterChange = (newValue: string | number | string[] | number[] | { [key: string]: any }) => {
    onFilterChange(name, newValue);
  };

  return <FilterComponent label={label} value={value} onChange={handleFilterChange} disabled={disabled} />;
};
