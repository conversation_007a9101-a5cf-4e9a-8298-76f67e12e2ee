import { PeekSelect, PeekSelectOption } from "@piiqtechnologies/ui-components";
import { FilterFieldComponentProps } from "./FilterField";
import React from "react";

interface RadiusSelectorProps extends FilterFieldComponentProps {
  onChange: (value: string) => void;
  value: string;
}

const REDIUS_OPTIONS = [
  { value: "", label: "Select radius" },
  { value: "5", label: "5 miles" },
  { value: "10", label: "10 miles" },
  { value: "15", label: "15 miles" },
  { value: "20", label: "20 miles" },
  { value: "25", label: "25 miles" },
  { value: "30", label: "30 miles" },
];

export const RadiusSelector: React.FC<RadiusSelectorProps> = ({ label, value, onChange, disabled }) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.target.value);
  };

  return (
    <PeekSelect
      size="small"
      sx={{ py: 0.3 }}
      fullWidth
      displayEmpty
      renderValue={(value) => (value ? `Within (${value} mi)` : label)}
      value={value}
      disabled={disabled}
      onChange={handleChange}
    >
      {REDIUS_OPTIONS.map((option) => (
        <PeekSelectOption key={option.value} value={option.value}>
          {option.label}
        </PeekSelectOption>
      ))}
    </PeekSelect>
  );
};

export default RadiusSelector;
