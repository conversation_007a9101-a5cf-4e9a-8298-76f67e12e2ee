import MultipleSelectorWithCount from "@common-components/multiple-selector-with-count";
import { useInsightsAmenities } from "@piiqtechnologies/ui-lib";
import React, { useEffect, useMemo } from "react";
import { FilterFieldComponentProps } from "./FilterField";

interface AmenitiesSelectorProps extends FilterFieldComponentProps {
  onChange: (value: string[]) => void;
  value: string[];
}

export const AmenitiesSelector: React.FC<AmenitiesSelectorProps> = ({ label, value, onChange, disabled }) => {
  const [getAmenities, amenities] = useInsightsAmenities();

  useEffect(() => {
    getAmenities();
  }, []);

  const options = useMemo(() => {
    return amenities
      ? amenities.map((a) => {
          return {
            label: a.name,
            value: a.name,
          };
        })
      : [];
  }, [amenities]);

  return (
    <MultipleSelectorWithCount
      label={label}
      options={options}
      selected={Array.isArray(value) ? value : []}
      onSelect={onChange}
      disabled={disabled}
    />
  );
};
