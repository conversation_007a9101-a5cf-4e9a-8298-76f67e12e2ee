import React, { useEffect, useMemo } from "react";
import { FilterFieldComponentProps } from "./FilterField";
import MultipleSelectorWithCount from "@common-components/multiple-selector-with-count";
import { InsightsYearBuilt, useInsightsYearsBuilt } from "@piiqtechnologies/ui-lib";

interface BuiltYearSelectorProps extends FilterFieldComponentProps {
  onChange: (value: InsightsYearBuilt[]) => void;
  value: InsightsYearBuilt[];
}

export const BuiltYearSelector: React.FC<BuiltYearSelectorProps> = ({ label, value, onChange, disabled }) => {
  const [getYearsBuilt, yearsBuilt] = useInsightsYearsBuilt();

  useEffect(() => {
    getYearsBuilt();
  }, []);

  const options = useMemo(() => {
    return yearsBuilt
      ? yearsBuilt.map((year: InsightsYearBuilt) => ({
          label: `${year.min} - ${year.max}`,
          value: `${year.min}-${year.max}`,
        }))
      : [];
  }, [yearsBuilt]);

  const onYearChange = (selectedYears: string[]) => {
    const formattedYears = selectedYears.map((year) => {
      const [min, max] = year.split("-");
      return { min: parseInt(min, 10), max: parseInt(max, 10) };
    });
    onChange(formattedYears);
  };

  const selectedYears = useMemo(() => {
    return value.map((year) => `${year.min}-${year.max}`);
  }, [value]);

  return (
    <MultipleSelectorWithCount
      label={label}
      options={options}
      selected={selectedYears}
      onSelect={onYearChange}
      disabled={disabled}
    />
  );
};
