import { <PERSON><PERSON><PERSON><PERSON>, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import React from "react";
import { AmenitiesSelector } from "./AmenitiesSelector";
import { BuildingTypeSelector } from "./BuildingTypeSelector";
import { BuiltYearSelector } from "./BuiltYearSelector";
import { FilterField, FilterFieldComponentProps } from "./FilterField";
import { LayoutSelector } from "./LayoutSelector";
import RadiusSelector from "./RadiusSelector";
import { SFSelector } from "./SFSelector";
import UnitsSelector from "./UnitsSelector";

export type InsightsFilter = {
  location: google.maps.LatLngLiteral;
  radius: string;
  numberOfUnits: string;
  buildingType: string[];
  layout: string[];
  amenities: string[];
  yearBuilt: string[];
  sf: string[];
};

export const InsightFilters = ({ filters, setFilters, currentTab }) => {
  // All available filters
  const allFilters: { name: string; label: string; FilterComponent: React.FC<FilterFieldComponentProps> }[] = [
    {
      name: "radius",
      label: "Radius",
      FilterComponent: RadiusSelector,
    },
    {
      name: "buildingType",
      label: "Building Type",
      FilterComponent: BuildingTypeSelector,
    },
    {
      name: "numberOfUnits",
      label: "No. of Units",
      FilterComponent: UnitsSelector,
    },
    {
      name: "layout",
      label: "Layout",
      FilterComponent: LayoutSelector,
    },
    {
      name: "sf",
      label: "SF per Unit",
      FilterComponent: SFSelector,
    },
    {
      name: "amenities",
      label: "Amenities",
      FilterComponent: AmenitiesSelector,
    },
    {
      name: "yearBuilt",
      label: "Year Built",
      FilterComponent: BuiltYearSelector,
    },
  ];

  return (
    <PeekStack direction="row" alignItems="center" spacing={0.5}>
      <PeekTypography variant="body1" fontWeight={500} width={120}>
        Map Search Filters:
      </PeekTypography>
      <PeekGrid container spacing={0.4} alignItems="center">
        {allFilters.map(({ name, label, FilterComponent }) => (
          <PeekGrid item key={name} md="auto">
            <FilterField
              name={name}
              label={label}
              value={filters[name]}
              onFilterChange={(name, value) => {
                setFilters({
                  ...filters,
                  [name]: value,
                });
              }}
              FilterComponent={FilterComponent}
              // disabled={!locationSelected}
            />
          </PeekGrid>
        ))}
      </PeekGrid>
    </PeekStack>
  );
};

export default InsightFilters;
