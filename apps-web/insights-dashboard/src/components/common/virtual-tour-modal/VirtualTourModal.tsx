import {
  <PERSON>eekBox,
  PeekButton,
  PeekDialog,
  PeekDialogContent,
  PeekScreenLoader,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import React, { useState } from "react";

interface VirtualTourModalProps {
  open: boolean;
  onClose: () => void;
  virtualTourUrl: string;
  title?: string;
}

export const VirtualTourModal: React.FC<VirtualTourModalProps> = ({
  open,
  onClose,
  virtualTourUrl,
  title = "Virtual Tour",
}) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const handleIframeLoad = () => {
    setLoading(false);
  };

  const handleIframeError = () => {
    setLoading(false);
    setError(true);
  };

  const handleOpenExternal = () => {
    window.open(virtualTourUrl, "_blank");
    onClose();
  };

  return (
    <PeekDialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      hasCloseIcon
      title={title}
      sx={{
        "& .MuiDialog-paper": {
          height: "90vh",
          maxHeight: "90vh",
        },
        zIndex: 999999,
      }}
    >
      <PeekDialogContent sx={{ p: 0, height: "100%", position: "relative" }}>
        {loading && <PeekScreenLoader />}
        {error && (
          <PeekStack
            direction="column"
            alignItems="center"
            justifyContent="center"
            spacing={2}
            sx={{
              height: "100%",
              backgroundColor: theme.palette.background.paper,
              p: 3,
            }}
          >
            <PeekTypography variant="h6" color="error">
              {!!error && "Failed to load Virtual Tour"}
            </PeekTypography>
            <PeekTypography variant="body2" color="textSecondary" textAlign="center">
              {!!error && "Please check your internet connection and try again."}
            </PeekTypography>
            <PeekStack direction="row" spacing={2}>
              <PeekButton variant="contained" onClick={handleOpenExternal}>
                Open in New Tab
              </PeekButton>
            </PeekStack>
          </PeekStack>
        )}

        {!error && (
          <PeekBox
            height="100%"
            width="100%"
            overflow="hidden"
            sx={{
              borderRadius: 1,
              "& iframe": {
                border: "none",
              },
            }}
          >
            <iframe
              src={virtualTourUrl}
              width="100%"
              height="100%"
              frameBorder="0"
              allowFullScreen
              allow="clipboard-write; gyroscope; accelerometer; magnetometer; camera; microphone;"
              title={title}
              onLoad={handleIframeLoad}
              onError={handleIframeError}
            />
          </PeekBox>
        )}
      </PeekDialogContent>
    </PeekDialog>
  );
};

export default VirtualTourModal;
