import { styled } from "@mui/material/styles";
import { PeekStack, PeekTypography, theme } from "@piiqtechnologies/ui-components";
import { AlertCircle } from "@piiqtechnologies/svg-icons";
import React from "react";

const PeekErrorIcon = styled(AlertCircle)(({}) => ({
  color: theme.palette.error.main,
}));

interface NoDataFoundProps {
  message: string;
  error?: boolean;
}

const NoDataFound: React.FC<NoDataFoundProps> = ({ message, error }) => {
  return (
    <PeekStack
      spacing={0.6}
      p={3}
      justifyContent="center"
      alignItems="center"
      direction="row"
      data-testid={`no-data-found${(error && "-error") || ""}`}
    >
      {error && <PeekErrorIcon />}
      <PeekTypography variant="h4" color={(error && "error") || "primary"}>
        {message}
      </PeekTypography>
    </PeekStack>
  );
};

export default NoDataFound;
