import {
  PeekCheckbox,
  PeekSelect,
  PeekSelectOption,
  PeekStack,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import React from "react";

interface MultipleSelectOption {
  label: string;
  value: string;
}

interface MultipleSelectorWithCountProps {
  label: string;
  options: MultipleSelectOption[];
  selected: string[];
  onSelect: (options: string[]) => void;
  sx?: React.CSSProperties;
  disabled?: boolean;
}

const MultipleSelectorWithCount: React.FC<MultipleSelectorWithCountProps> = ({
  label,
  options,
  selected,
  onSelect,
  sx,
  disabled,
}) => {
  const theme = useTheme();

  return (
    <PeekSelect
      value={selected}
      onChange={(event) => onSelect(event.target.value)}
      showBorder
      multiple
      displayEmpty
      placeholder={label}
      style={sx}
      disabled={disabled}
      renderValue={(value) => {
        const isEmpty = !value || (Array.isArray(value) && value.length === 0);

        if (isEmpty) {
          return <PeekTypography>{label}</PeekTypography>;
        }

        return (
          <PeekStack direction="row" width="fit-content" spacing={1}>
            <PeekTypography>{label}</PeekTypography>
            <PeekTypography
              sx={{
                backgroundColor: theme.palette.secondary.main,
                borderRadius: "50%",
                width: "16px",
                height: "16px",
                color: theme.palette.common.white,
                letterSpacing: "1px",
                lineHeight: "16px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "10px",
              }}
            >
              {Array.isArray(value) && value ? value.length : "0"}
            </PeekTypography>
          </PeekStack>
        );
      }}
      inputProps={{ "aria-label": `${label} Selector` }}
      MenuProps={{
        disablePortal: true,
        sx: {
          "& .MuiPaper-root": {
            borderRadius: "8px",
            minWidth: "160px !important",
          },
          "& .MuiMenuItem-root": {
            justifyContent: "space-between !important",
          },
        },
      }}
      sx={{
        minHeight: "fit-content",
        height: "42px",
        fontWeight: "fontWeightMedium",
        borderLeft: `1px solid ${theme.palette.grey[200]}`,
        ".MuiMenuItem-root": {
          padding: 0,
        },
        ".Mui-selected": { backgroundColor: `${theme.palette.common.white} !important` },
      }}
    >
      {options &&
        options.map(({ label, value }) => {
          const selectedOption = !!(selected && selected.includes(value));
          return (
            <PeekSelectOption key={label} value={value}>
              <PeekStack direction="row" width="100%" alignItems="center">
                <PeekCheckbox
                  checked={selectedOption}
                  sx={{
                    svg: {
                      color: selectedOption ? `${theme.palette.secondary.main} !important` : "inherit",
                    },
                  }}
                />
                <PeekTypography>{label}</PeekTypography>
              </PeekStack>
            </PeekSelectOption>
          );
        })}
    </PeekSelect>
  );
};

export default MultipleSelectorWithCount;
