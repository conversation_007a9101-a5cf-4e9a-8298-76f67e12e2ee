import React from "react";
import { Search as SearchIcon } from "@piiqtechnologies/svg-icons";
import {
  PeekTextField,
  PeekInputAdornment,
  useTheme,
  PeekTypography,
  PeekTextFieldProps,
} from "@piiqtechnologies/ui-components";

type SearchTextProps = PeekTextFieldProps & {
  searchText: string;
  placeholder?: string;
  fullWidth?: boolean;
  disabled?: boolean;
};

export const SearchText = ({ searchText, onChange, placeholder, fullWidth, disabled, ...props }: SearchTextProps) => {
  const theme = useTheme();

  return (
    <PeekTextField
      sx={{
        m: 0,
        minWidth: fullWidth ? "100%" : "fit-content",
        backgroundColor: "common.white",
        borderRadius: theme.spacing(2),
        "& .MuiInput-root": {
          mt: 0,
          height: theme.spacing(3.8),
          minHeight: "unset !important",
          borderRadius: theme.spacing(2),
        },
        input: {
          marginRight: theme.spacing(1.6),
          "&::placeholder": {
            opacity: 1,
            color: theme.palette.grey[300],
          },
        },
      }}
      disabled={disabled}
      variant="standard"
      value={searchText || ""}
      fullWidth={fullWidth}
      {...props}
      InputLabelProps={{ required: true }}
      InputProps={{
        ...props.InputProps,
        placeholder: placeholder || "Search",
        disableUnderline: true,
        "aria-label": "Search",
        startAdornment: (
          <PeekInputAdornment position="end">
            <PeekTypography variant="h3" color="text" display="flex" sx={{ ml: 1 }}>
              <SearchIcon color={theme.palette.grey[300]} />
            </PeekTypography>
          </PeekInputAdornment>
        ),
      }}
    />
  );
};

export default SearchText;
