import { Check, PlayCircle } from "@piiqtechnologies/svg-icons";
import { PeekBox, PeekStack, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import React, { useState } from "react";
import { VirtualTourModal } from "../virtual-tour-modal";
import { useAnalytics } from "@piiqtechnologies/ui-lib";

export type StatusLevel = "high" | "medium" | "low" | "na";

interface StatusIndicatorProps {
  status: StatusLevel;
  size?: number;
  virtualTourUrl?: string;
  amenityName?: string;
  propertyName?: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 20,
  virtualTourUrl,
  amenityName,
  propertyName,
}) => {
  const theme = useTheme();
  const [modalOpen, setModalOpen] = useState(false);
  const analytics = useAnalytics();

  const handlePlayClick = () => {
    if (virtualTourUrl) {
      analytics?.track("amenity_virtual_tour_clicked", {
        amenityName,
        propertyName,
        url: virtualTourUrl,
      });
      setModalOpen(true);
    }
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  const getColors = (status: StatusLevel) => {
    switch (status) {
      case "high":
        return {
          background: "rgba(85, 187, 24, 0.2)",
          border: "#55BB18",
          icon: "#55BB18",
        };
      case "medium":
        return {
          background: "rgba(255, 152, 0, 0.2)",
          border: "#FF9800",
          icon: "#FF9800",
        };
      case "low":
        return {
          background: "rgba(244, 67, 54, 0.2)",
          border: "#F44336",
          icon: "#F44336",
        };
      default:
        return {
          background: "rgba(158, 158, 158, 0.1)",
          border: theme.palette.grey[400],
          icon: theme.palette.grey[400],
        };
    }
  };

  const colors = getColors(status);

  return (
    <>
      <PeekStack
        direction="row"
        spacing={0}
        sx={{
          border: `1px solid ${colors.border}`,
          borderRadius: "4px",
          overflow: "hidden",
          width: size * 2.5,
          height: size,
          backgroundColor: colors.background,
        }}
      >
        <PeekBox
          sx={{
            flex: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            minHeight: "100%",
          }}
        >
          <Check
            style={{
              fontSize: size * 0.6,
              color: colors.icon,
            }}
          />
        </PeekBox>
        {virtualTourUrl && (
          <>
            <PeekBox
              sx={{
                width: "1px",
                backgroundColor: colors.border,
                minHeight: "100%",
              }}
            />

            <PeekBox
              sx={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "100%",
                cursor: virtualTourUrl ? "pointer" : "default",
                transition: "all 0.2s ease-in-out",
                "&:hover": virtualTourUrl
                  ? {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                      transform: "scale(1.1)",
                      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
                    }
                  : {},
              }}
              onClick={virtualTourUrl ? handlePlayClick : undefined}
            >
              <PlayCircle
                style={{
                  fontSize: size * 0.6,
                  color: colors.icon,
                  opacity: virtualTourUrl ? 1 : 0.7,
                  filter: virtualTourUrl ? "drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))" : "none",
                }}
              />
            </PeekBox>
          </>
        )}
      </PeekStack>
      {virtualTourUrl && (
        <VirtualTourModal
          open={modalOpen}
          onClose={handleCloseModal}
          virtualTourUrl={virtualTourUrl}
          title={`${amenityName ? `${amenityName} - ` : ""}${propertyName || "Virtual Tour"}`}
        />
      )}
    </>
  );
};

export default StatusIndicator;
