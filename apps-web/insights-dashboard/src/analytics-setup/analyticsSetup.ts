import Analytics, { AnalyticsInstance } from "analytics";
import mixpanelPlugin from "@analytics/mixpanel";
import { analyticsPlugin as peekAnalyticsPlugin } from "@piiqtechnologies/ui-lib";

const analyticsTools = process.env.PEEK_APP_ANALYTICS_TOOLS || "";

export const analytics: AnalyticsInstance = Analytics({
  app: "insights-dashboard",
  debug: false,
  plugins: [
    mixpanelPlugin({
      token: process.env.PEEK_APP_MIXPANEL_TOKEN,
      enabled: analyticsTools.includes("MIXPANEL"),
    }),
    peekAnalyticsPlugin({
      appId: process.env.PEEK_APP_ANALYTICS_APP_ID,
      enabled: analyticsTools.includes("PEEK"),
    }),
  ],
});

export default analytics;
