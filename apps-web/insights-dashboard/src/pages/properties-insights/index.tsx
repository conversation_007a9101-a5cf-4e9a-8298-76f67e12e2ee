import { BedroomOverviews, LeaseOverviewTable } from "@components/bedroom-overviews";
import { Period, PeriodSelector } from "@components/common/date-range-picker";
import { PropertiesAverages, PropertiesAveragesFilters } from "@components/properties-averages";
import { InsightsContext } from "@layouts/insights";
import { PeekGrid, PeekStack } from "@piiqtechnologies/ui-components";
import { useAnalytics } from "@piiqtechnologies/ui-lib";
import { endOfWeek, startOfWeek, sub } from "date-fns";
import React, { useEffect, useState } from "react";
import { useOutletContext } from "react-router-dom";

export type PropertyAverageFilter = {
  buildingType: string[];
  layout: string[];
  unitAmenities: string[];
  sf: string[];
  amenities: string[];
};

export const PropertiesInsights = () => {
  const { selectedProperties, locationSelected } = useOutletContext<InsightsContext>();
  const analytics = useAnalytics();

  const [propertiesAverageFilters, setPropertiesAverageFilters] = useState<PropertyAverageFilter>({
    buildingType: [],
    layout: [],
    unitAmenities: [],
    sf: [],
    amenities: [],
  });

  const [dateRange, setDateRange] = useState<Period>(() => {
    const today = new Date();
    const endOfLastCompleteWeek = endOfWeek(sub(today, { days: 7 }), { weekStartsOn: 1 });
    const startOfPeriod = sub(endOfLastCompleteWeek, { days: 4 * 7 - 1 });
    const startOfCompleteWeek = startOfWeek(startOfPeriod, { weekStartsOn: 1 });
    return { startDate: startOfCompleteWeek, endDate: endOfLastCompleteWeek };
  });

  useEffect(() => {
    analytics.page({
      title: "Peek Insights Dashboard || Properties Insights",
    });
  }, []);

  const handleDateRangeChange = (newDateRange: Period) => {
    setDateRange(newDateRange);
    analytics.track("date_range_changed", {
      startDate: newDateRange.startDate.toISOString(),
      endDate: newDateRange.endDate.toISOString(),
    });
  };

  return (
    <PeekGrid container direction="column" rowSpacing={3}>
      <PeekGrid item xs={12}>
        <PeekStack direction="row" spacing={1} alignItems="center" justifyContent="flex-end" width={"100%"}>
          <PropertiesAveragesFilters filters={propertiesAverageFilters} setFilters={setPropertiesAverageFilters} />
          <PeriodSelector value={dateRange} onChange={handleDateRangeChange} disabled={!locationSelected} />
        </PeekStack>
      </PeekGrid>
      <PeekGrid item xs={12}>
        <PropertiesAverages dateRange={dateRange} graphFilters={propertiesAverageFilters} />
      </PeekGrid>
      <PeekGrid item xs={12}>
        <LeaseOverviewTable selectedProperties={selectedProperties} dateRange={dateRange} />
      </PeekGrid>
      <PeekGrid item xs={12}>
        <BedroomOverviews selectedProperties={selectedProperties} dateRange={dateRange} />
      </PeekGrid>
    </PeekGrid>
  );
};

export default PropertiesInsights;
