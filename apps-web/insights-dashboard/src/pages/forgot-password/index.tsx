import React from "react";
import ForgotPasswordForm from "@components/forgot-password";
import { useNavigate } from "react-router-dom";
import { PATHS } from "@constants/Paths";

interface ForgotPasswordProps {}

export const ForgotPassword: React.FC<ForgotPasswordProps> = () => {
  const navigate = useNavigate();

  const onPasswordResetRequestSuccess = () => {
    navigate(PATHS.LOGIN);
  };

  return <ForgotPasswordForm onSuccess={onPasswordResetRequestSuccess} />;
};

export default ForgotPassword;
