import React from "react";
import { PeekButton, PeekStack } from "@piiqtechnologies/ui-components";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useIntl } from "react-intl";
import Error, { ERROR_CODES } from "@components/error";
import messages from "./messages.i18n";
import { PATHS } from "@constants/Paths";

interface SSOLoginErrorProps {}

export const SSOLoginError: React.FC<SSOLoginErrorProps> = () => {
  const [searchParams] = useSearchParams();
  const intl = useIntl();
  const errorCode = searchParams.get("message");
  const navigate = useNavigate();

  const onBackToSignIn = () => {
    navigate(PATHS.LOGIN);
  };

  return (
    <PeekStack flexDirection="column" spacing={5} minHeight={210} justifyContent="space-between">
      <Error errorCode={errorCode as ERROR_CODES} />
      <PeekButton fullWidth color="primary" variant="contained" onClick={onBackToSignIn}>
        {intl.formatMessage(messages.backToSignIn)}
      </PeekButton>
    </PeekStack>
  );
};

export default SSOLoginError;
