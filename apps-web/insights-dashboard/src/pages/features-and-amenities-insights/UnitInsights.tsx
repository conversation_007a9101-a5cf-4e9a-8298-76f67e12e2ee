// import { InsightsContext } from "@layouts/insights";
// import { useError, useInsightsUnitsAndFeatures } from "@piiqtechnologies/ui-lib";
// import React, { useEffect } from "react";
// import { useOutletContext } from "react-router-dom";
// import { InsightsTable } from "../../components/amenities-insights/amenities-insights-table";

// export const UnitInsights = () => {
//   const [fetchUnitsAndFeatures, unitsAndFeatures, loadingUnitsAndFeatures, errorUnitsAndFeatures] =
//     useInsightsUnitsAndFeatures();
//   const { insightsFilters } = useOutletContext<InsightsContext>();
//   const { setError } = useError();

//   useEffect(() => {
//     fetchUnitsAndFeatures({
//       filters: insightsFilters,
//     });
//   }, [insightsFilters]);

//   useEffect(() => {
//     if (errorUnitsAndFeatures) {
//       setError("Could not load unit feature insights data");
//     }
//   }, [errorUnitsAndFeatures, setError]);

//   return (
//     <InsightsTable
//       title="UNIT FEATURE INSIGHTS"
//       data={unitsAndFeatures ?? []}
//       showFilters={false}
//       isLoading={loadingUnitsAndFeatures}
//     />
//   );
// };
