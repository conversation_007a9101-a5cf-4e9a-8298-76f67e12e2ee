import { Peek<PERSON>rid, PeekStack, PeekTypography } from "@piiqtechnologies/ui-components";
import React, { useEffect, useState } from "react";
import { BuildingAmenityInsights } from "@components/amenities-insights";
import { useAnalytics } from "@piiqtechnologies/ui-lib";
import { PeriodSelector, Period } from "@components/common/date-range-picker";
import { InsightsContext } from "@layouts";
import { useOutletContext } from "react-router-dom";
import { endOfWeek, startOfWeek, sub } from "date-fns";

export const FeaturesAndAmenitiesInsights: React.FC<{}> = () => {
  const { selectedProperties } = useOutletContext<InsightsContext>();
  const analytics = useAnalytics();

  const [dateRange, setDateRange] = useState<Period>(() => {
    const today = new Date();
    const endOfLastCompleteWeek = endOfWeek(sub(today, { days: 7 }), { weekStartsOn: 1 });
    const startOfPeriod = sub(endOfLastCompleteWeek, { days: 4 * 7 - 1 });
    const startOfCompleteWeek = startOfWeek(startOfPeriod, { weekStartsOn: 1 });
    return { startDate: startOfCompleteWeek, endDate: endOfLastCompleteWeek };
  });

  const handleDateRangeChange = (newDateRange: Period) => {
    setDateRange(newDateRange);
    analytics.track("date_range_changed", {
      startDate: newDateRange.startDate.toISOString(),
      endDate: newDateRange.endDate.toISOString(),
    });
  };

  useEffect(() => {
    analytics.page({
      title: "Peek Insights Dashboard || Features and Amenities Insights",
    });
  }, []);

  return (
    <PeekGrid container direction="column" rowSpacing={3}>
      <PeekGrid item xs={12}>
        <PeekStack direction="row" spacing={1} alignItems="center" justifyContent="flex-end" width={"100%"}>
          <PeriodSelector value={dateRange} onChange={handleDateRangeChange} disabled={!selectedProperties?.length} />
        </PeekStack>
      </PeekGrid>
      <PeekGrid item xs={12}>
        <BuildingAmenityInsights dateRange={dateRange} />
      </PeekGrid>
    </PeekGrid>
  );
};

export default FeaturesAndAmenitiesInsights;
