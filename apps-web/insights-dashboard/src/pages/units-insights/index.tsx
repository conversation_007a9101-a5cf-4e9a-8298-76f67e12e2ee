import { Period } from "@common-components/date-range-picker";
import TablePanelContainer from "@components/common/table/TablePanelContainer";
import { UnitInsightsTable } from "@components/unit-insights";
import { InsightsContext } from "@layouts";
import { PeekDivider, PeekGrid, PeekPaper, PeekStack, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import { RefetchProvider, useAnalytics } from "@piiqtechnologies/ui-lib";
import { subDays } from "date-fns";
import React, { useEffect, useState } from "react";
import { useOutletContext } from "react-router-dom";

interface UnitInsightsProps {}

const UnitInsights: React.FC<UnitInsightsProps> = () => {
  const theme = useTheme();
  const analytics = useAnalytics();

  useEffect(() => {
    analytics.page({
      title: "Peek Insights Dashboard || Unit Insights",
    });
  }, []);

  return (
    <PeekGrid container direction="column" rowSpacing={3}>
      <PeekGrid item xs={12}>
        <PeekPaper sx={{ padding: theme.spacing(1.5), width: "100%" }}>
          <PeekStack direction="column" spacing={1.5} sx={{ padding: theme.spacing(1.5) }}>
            <PeekStack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
              <PeekTypography variant="h2" color="grey.400" fontWeight="fontWeightMedium">
                UNIT LEVEL INSIGHTS
              </PeekTypography>
            </PeekStack>
          </PeekStack>
          <PeekDivider light orientation="horizontal" />
          <RefetchProvider>
            <TablePanelContainer>
              <UnitInsightsTable />
            </TablePanelContainer>
          </RefetchProvider>
        </PeekPaper>
      </PeekGrid>
    </PeekGrid>
  );
};

export default UnitInsights;
