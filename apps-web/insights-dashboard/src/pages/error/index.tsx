import React from "react";
import { PeekStack } from "@piiqtechnologies/ui-components";
import ErrorComponent, { ERROR_CODES } from "@components/error";

interface ErrorProps {
  errorCode: ERROR_CODES;
}

export const Error: React.FC<ErrorProps> = ({ errorCode }) => {
  return (
    <PeekStack flexDirection="column" justifyContent="space-between">
      <ErrorComponent errorCode={errorCode as ERROR_CODES} />
    </PeekStack>
  );
};

export default Error;
