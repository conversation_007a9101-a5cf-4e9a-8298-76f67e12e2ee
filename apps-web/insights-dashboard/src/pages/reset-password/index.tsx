import React from "react";
import ResetPasswordForm from "@components/reset-password";
import { useNavigate, useSearchParams } from "react-router-dom";
import { PATHS } from "@constants/Paths";

interface ResetPasswordProps {}

export const ResetPassword: React.FC<ResetPasswordProps> = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");

  const onResetPassword = () => {
    navigate(PATHS.LOGIN, { replace: true });
  };

  return <ResetPasswordForm onResetPasswordSuccess={onResetPassword} resetToken={token} />;
};

export default ResetPassword;
