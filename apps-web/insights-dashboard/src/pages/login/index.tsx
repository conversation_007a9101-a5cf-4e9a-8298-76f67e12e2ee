import LoginForm from "@components/login";
import { <PERSON>SSO, MicrosoftSSO } from "@components/sso-login";
import { PATHS } from "@constants/Paths";
import { PeekButton, PeekStack } from "@piiqtechnologies/ui-components";
import { LoginResponse, useLoader } from "@piiqtechnologies/ui-lib";
import { useAuthContext } from "@stores/AuthContext";
import React, { useEffect } from "react";
import { useIntl } from "react-intl";
import { useNavigate, useSearchParams } from "react-router-dom";
import messages from "./messages.i18n";

interface LoginProps {}

export const Login: React.FC<LoginProps> = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const { onLogin } = useAuthContext();
  const [queryParams] = useSearchParams();
  const { setLoader } = useLoader();

  useEffect(() => {
    const token = queryParams.get("token");

    if (!token) return;
    setLoader(true);
    onLogin({ token: token }, true);
    setLoader(false);
    navigate(PATHS.HOME, {
      replace: true,
    });
  }, [queryParams]);

  const handleLogin = (loggedInUser: LoginResponse) => {
    onLogin(loggedInUser, false);
  };

  const onForgotPassword = () => {
    navigate(PATHS.FORGOT_PASSWORD);
  };

  return (
    <PeekStack direction="column" spacing={1.2}>
      <LoginForm onLoginSuccess={handleLogin} />
      <PeekButton
        color="primary"
        disableRipple
        sx={{ ":hover": { backgroundColor: "transparent" } }}
        onClick={onForgotPassword}
      >
        {intl.formatMessage(messages.forgotPassword)}
      </PeekButton>
      <PeekStack direction="row" spacing={1.5} pt={0.9}>
        <GoogleSSO />
        <MicrosoftSSO />
      </PeekStack>
    </PeekStack>
  );
};

export default Login;
