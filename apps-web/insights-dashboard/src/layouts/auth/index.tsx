import { PATHS } from "@constants/Paths";
import { useAuthContext } from "@stores/AuthContext";
import React from "react";
import { Outlet, Navigate, useLocation } from "react-router-dom";

export interface AuthLayoutContext {}

export const AuthLayout: React.FC = () => {
  const { user } = useAuthContext();
  const location = useLocation();
  return !!user ? <Outlet /> : <Navigate to={PATHS.LOGIN} replace state={{ redirectTo: location }} />;
};

export default AuthLayout;
