import React from "react";
import { Peek<PERSON>lert, PeekSnackbar, PeekTypography, PeekScreenLoader, PeekBox } from "@piiqtechnologies/ui-components";
import { Outlet } from "react-router-dom";
import { useError, useLoader, useNotification } from "@piiqtechnologies/ui-lib";

interface AppLayoutProps {}

export const AppLayout: React.FC<AppLayoutProps> = ({}) => {
  const { error, setError } = useError();
  const { notification, setNotification } = useNotification();
  const { loader, queue } = useLoader();

  const handleCloseAlert = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === "clickaway") {
      return;
    }
    setError(null);
    setNotification(null);
  };

  return (
    <PeekBox sx={{ backgroundColor: "grey.50", overflowY: "auto" }} height="100vh">
      {loader && <PeekScreenLoader layered />}
      <Outlet />
      <PeekSnackbar
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
        open={!!error || !!notification}
        autoHideDuration={3000}
        data-testid="app-error"
        onClose={handleCloseAlert}
      >
        <PeekBox>
          <PeekAlert
            sx={{ alignItems: "center" }}
            onClose={handleCloseAlert}
            severity={(error && "error") || "success"}
          >
            <PeekTypography variant="body1">{error || notification}</PeekTypography>
          </PeekAlert>
        </PeekBox>
      </PeekSnackbar>
    </PeekBox>
  );
};

export default AppLayout;
