import { Peek<PERSON><PERSON>, PeekStack, PeekTab, PeekTabs, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import React from "react";
import { MessageDescriptor } from "react-intl";

export interface TabDescriptor {
  value: string;
  label: MessageDescriptor | string;
  icon?: React.ReactElement;
  labelBadgeContent?: Record<string, string>;
  disabled?: boolean;
}

interface TabsLayoutProps {
  tabs: TabDescriptor[];
  onTabChange: (tabValue: string) => void;
  activeTab: string;
  TabContent: React.ReactNode;
  tabSpacing?: boolean;
  tabContentSize?: "small" | "medium";
  tabSxProps?: Record<string, any>;
  children?: React.ReactNode;
}

export const TabsLayout = ({
  tabs,
  onTabChange,
  activeTab,
  TabContent,
  tabSpacing = true,
  tabContentSize = "medium",
  tabSxProps = {},
  children,
}: TabsLayoutProps) => {
  const theme = useTheme();

  const handleTabChange = (_event: any, value: string) => {
    onTabChange(value);
  };

  if (!activeTab) {
    return null;
  }

  return (
    <PeekStack
      spacing={3}
      padding={3}
      direction="column"
      sx={{ minHeight: "inherit", maxHeight: "inherit", overflow: "hidden" }}
    >
      <PeekTabs
        value={activeTab}
        orientation="horizontal"
        onChange={handleTabChange}
        aria-label="Tabs Tabs"
        sx={{
          position: "relative",
          "& .MuiTabs-flexContainer": { gap: tabSpacing ? theme.spacing(2.5) : 0, justifyContent: "center" },
        }}
      >
        {tabs.map(({ value, label, labelBadgeContent, disabled, icon: Icon }) => {
          const id = `${value}-tab`;
          return (
            <PeekTab
              key={`${value}-tab`}
              id={id}
              disabled={disabled}
              icon={Icon}
              iconPosition="start"
              aria-controls={id}
              sx={tabSxProps}
              label={
                <PeekTypography
                  variant={tabContentSize === "medium" ? "h4" : "h5"}
                  display="contents"
                  alignItems="center"
                >
                  {typeof label === "string" ? label : label.defaultMessage}
                  {labelBadgeContent?.count !== undefined && (
                    <PeekChip
                      variant="filled"
                      label={
                        <PeekTypography variant="body2" fontWeight="fontWeightBold">
                          {labelBadgeContent?.count}
                        </PeekTypography>
                      }
                      //@ts-ignore
                      color={labelBadgeContent?.color || (activeTab === value ? "primary" : "info")}
                      sx={{
                        ml: 0.5,
                      }}
                    />
                  )}
                </PeekTypography>
              }
              value={value}
            />
          );
        })}
      </PeekTabs>
      {children}
      {(!!TabContent && TabContent) || null}
    </PeekStack>
  );
};

export default TabsLayout;
