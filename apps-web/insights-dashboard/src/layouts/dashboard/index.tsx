import React, { useMemo } from "react";
import { Outlet } from "react-router-dom";
import {
  PeekGrid,
  PeekContainer,
  PeekChip,
  PeekStack,
  PeekTypography,
  useTheme,
  keyframes,
  styled,
} from "@piiqtechnologies/ui-components";
import SideBarMenu from "@components/side-navbar";
import { AlertOctagon } from "@piiqtechnologies/svg-icons";
import { useIntl } from "react-intl";
import messages from "./messages.i18n";
import { useGoogleMapApi } from "@piiqtechnologies/ui-lib";

const ChipAnimation = keyframes`
  0% {
    box-shadow: none;
    outline-offset: 0;
  }
  100% {
    outline-color: rgba(255, 255, 255, 0);
    outline-offset: 10px;
  }
`;

const AnimatedChip = styled(PeekChip)(({ theme }) => ({
  zIndex: 10000,
  position: "absolute",
  top: "20px",
  left: "45vw",
  margin: "auto",
  outline: "1px solid",
  outlineColor: theme.palette.error.main,
  outlineOffset: 0,
  animation: `${ChipAnimation} 2s cubic-bezier(0.19, 1, 0.22, 1) infinite`,
}));

const DevEnvironmentAlert = () => {
  const intl = useIntl();
  const theme = useTheme();

  return (
    <AnimatedChip
      color="error"
      label={
        <PeekStack direction={"row"} gap={1} alignItems={"center"}>
          <AlertOctagon fontSize={20} />
          <PeekTypography variant="h3" fontWeight={theme.typography.fontWeightLight}>
            {intl.formatMessage(messages.dashboardDevEnvironment)}
          </PeekTypography>
        </PeekStack>
      }
    />
  );
};

export const DashboardLayout = () => {
  const isDevEnvironment = useMemo(() => process.env.PEEK_APP_ENVIRONMENT === "dev", []);
  const { mapAPiloaded } = useGoogleMapApi();

  if (!mapAPiloaded) {
    return null;
  }

  return (
    <PeekContainer maxWidth="xl">
      <SideBarMenu />
      <PeekGrid container pl={3} pr={0} py={5} rowSpacing={4} direction="column">
        {isDevEnvironment && <DevEnvironmentAlert />}
        <PeekGrid item xs={12} sm={12} md={12} lg={12} xl={12}>
          <Outlet />
        </PeekGrid>
      </PeekGrid>
    </PeekContainer>
  );
};

export default DashboardLayout;
