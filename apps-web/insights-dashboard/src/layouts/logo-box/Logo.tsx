import React from "react";
import { PeekStack } from "@piiqtechnologies/ui-components";
import { Peek, PeekIcon } from "@piiqtechnologies/svg-icons";
import { styled } from "@mui/material/styles";

const PeekLogo = styled(Peek)(({ theme }) => ({
  fontSize: theme.spacing(9),
  color: theme.palette.secondary.contrastText,
}));

const PeekImageIcon = styled(PeekIcon)(({ theme }) => ({
  fontSize: theme.spacing(2.5),
  color: theme.palette.secondary.contrastText,
}));

const LogoBox = styled(PeekStack)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
}));

export interface LogoProps {}

export const Logo: React.FC<LogoProps> = () => {
  return (
    <LogoBox direction="row" justifyContent="center" alignItems="center" spacing={1.5}>
      <PeekLogo />
      <PeekImageIcon />
    </LogoBox>
  );
};

export default Logo;
