import React, { useEffect } from "react";
import { PeekContainer, PeekGrid, PeekPaper, PeekStack } from "@piiqtechnologies/ui-components";
import { Outlet, useLocation } from "react-router-dom";
import Logo from "./Logo";
import { useAnalytics } from "@piiqtechnologies/ui-lib";

interface LogoBoxLayoutProps {}

export const LogoBoxLayout: React.FC<LogoBoxLayoutProps> = () => {
  const location = useLocation();
  const analytics = useAnalytics();

  useEffect(() => {
    analytics.page({
      title: "Peek Insights Dashboard",
    });
  }, [location]);

  return (
    <PeekContainer maxWidth="sm">
      <PeekStack height="100vh" direction="row" alignItems="center" justifyContent="center">
        <PeekStack direction="column" alignItems="center" justifyContent="space-between">
          <PeekPaper sx={{ width: 420 }} elevation={2}>
            <PeekGrid container>
              <PeekGrid item xs={12}>
                <Logo />
              </PeekGrid>
              <PeekGrid item xs={12} px={4} py={3}>
                <Outlet />
              </PeekGrid>
            </PeekGrid>
          </PeekPaper>
        </PeekStack>
      </PeekStack>
    </PeekContainer>
  );
};

export default LogoBoxLayout;
