import { PropertiesLocationSearch, PropertiesMap } from "@components/properties-map";
import { PropertiesOverview } from "@components/properties-overview";
import { PATHS } from "@constants/Paths";
import TabsLayout from "@layouts/tabs";
import { PeekGrid, PeekPaper } from "@piiqtechnologies/ui-components";
import { InsightsProperty, useAnalytics, useError, useInsightsProperties, useLoader } from "@piiqtechnologies/ui-lib";
import { isEmptyLocation } from "@utils";
import React, { useEffect, useMemo, useState } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { InsightFilters, InsightsFilter } from "@components/common/filters";
import messages from "./messages.i18n";

interface InsightsLayoutProps {}

export type InsightsContext = {
  insightsFilters: InsightsFilter;
  setInsightsFilters: React.Dispatch<React.SetStateAction<InsightsFilter>>;
  selectedProperties?: InsightsProperty[];
  setSelectedProperties?: React.Dispatch<React.SetStateAction<InsightsProperty[]>>;
  locationSelected?: boolean;
  currentTab?: string;
  showFilters?: boolean;
};
export type Amenity = {
  name: string;
};

export const InsightsLayout: React.FC<InsightsLayoutProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [tabValue, setTabValue] = useState(null!);
  const [selectedProperties, setSelectedProperties] = useState<InsightsProperty[]>([]);
  const analytics = useAnalytics();
  const [getProperties, properties, loadingProperties, errorProperties] = useInsightsProperties();
  const { setLoader } = useLoader();
  const { setError } = useError();

  const [insightsFilters, setInsightsFilters] = useState<InsightsFilter>({
    location: {
      lat: null,
      lng: null,
    },
    radius: undefined,
    numberOfUnits: "",
    buildingType: [],
    layout: [],
    amenities: [],
    yearBuilt: [],
    sf: [],
  });

  const locationSelected = useMemo(() => {
    return !isEmptyLocation(insightsFilters.location);
  }, [insightsFilters.location]);

  useEffect(() => {
    if (locationSelected) {
      getProperties(insightsFilters);
    }
  }, [locationSelected, insightsFilters]);

  useEffect(() => {
    setLoader(loadingProperties, "loadingProperties");
    return () => {
      setLoader(false, "loadingProperties");
    };
  }, [loadingProperties]);

  useEffect(() => {
    if (errorProperties) {
      setError(errorProperties.message || "An error occurred while fetching properties insights.");
    }
  }, [errorProperties]);

  const onSelectProperty = (propertyId: string) => {
    const property = properties.find((p) => p.id === propertyId);
    if (property) {
      // Check if property is already selected to prevent duplicates
      const isAlreadySelected = selectedProperties?.some((p) => p.id === propertyId);
      if (isAlreadySelected) {
        return;
      }

      setSelectedProperties((prev) => {
        // If we already have 5 properties (max slots), remove the first one and add new one
        if (prev.length >= 5) {
          return [...prev.slice(1), property];
        } else {
          return [...prev, property];
        }
      });
    }
  };

  const onRemoveProperty = (propertyId: string) => {
    setSelectedProperties((prev) => prev.filter((p) => p.id !== propertyId));
  };

  const onFiltersChange = (filters: InsightsFilter) => {
    setInsightsFilters(filters);
    analytics.track("insights_filters_changed", {
      filters,
    });
  };

  const INSIGHTS_TABS = useMemo(
    () => [
      {
        value: PATHS.PRORPERTIES_INSIGHTS,
        label: messages.propertiesInsights,
      },
      {
        value: PATHS.UNIT_INSIGHTS,
        label: messages.unitsInsights,
        disabled: !locationSelected,
      },
      {
        value: PATHS.FEATURES_AND_AMENITIES_INSIGHTS,
        label: messages.featuresAndAmenitiesInsights,
        disabled: !locationSelected,
      },
    ],
    [locationSelected],
  );

  useEffect(() => {
    const insightsURLSegments = location.pathname.split("/");
    const activeTabURL = insightsURLSegments.pop() || insightsURLSegments.pop();
    const isValidTabValue = INSIGHTS_TABS.map(({ value }) => value).includes(activeTabURL as PATHS);
    setTabValue((isValidTabValue && activeTabURL) || PATHS.PRORPERTIES_INSIGHTS);
  }, [location.pathname]);

  const handleTabChange = (value: PATHS) => {
    navigate(value);
    analytics.track("insights_tab_changed", {
      tab: value,
    });
  };

  if (!tabValue) {
    return null;
  }

  return (
    <TabsLayout
      activeTab={tabValue}
      tabs={INSIGHTS_TABS}
      TabContent={
        <Outlet
          context={{
            insightsFilters,
            setInsightsFilters: onFiltersChange,
            selectedProperties,
            setSelectedProperties,
            locationSelected,
            currentTab: tabValue,
            showFilters: false,
          }}
        />
      }
      onTabChange={handleTabChange}
    >
      <PeekGrid container direction="column" rowSpacing={3}>
        <PeekGrid item xs={12}>
          <PeekPaper sx={{ p: 1.5 }}>
            <PeekGrid spacing={1.5} container>
              <PeekGrid item xs={12} container direction="row" alignItems="center" columnSpacing={1}>
                <PeekGrid item xs={3}>
                  <PropertiesLocationSearch
                    onPlaceSelect={(location) => {
                      setInsightsFilters((prev) => ({
                        ...prev,
                        location,
                      }));
                    }}
                  />
                </PeekGrid>
                <PeekGrid item xs={9}>
                  <InsightFilters filters={insightsFilters} setFilters={onFiltersChange} currentTab={tabValue} />
                </PeekGrid>
              </PeekGrid>
              <PeekGrid item xs={12} height={400} sx={{ aspectRatio: "16/10" }}>
                <PropertiesMap
                  location={insightsFilters.location}
                  properties={properties || []}
                  selectedProperties={selectedProperties?.map((p) => p.id)}
                  onSelectProperty={onSelectProperty}
                />
              </PeekGrid>
            </PeekGrid>
          </PeekPaper>
        </PeekGrid>
        <PeekGrid item xs={12}>
          <PropertiesOverview
            properties={properties}
            selectedProperties={selectedProperties}
            onSelectProperty={onSelectProperty}
            onRemoveProperty={onRemoveProperty}
          />
        </PeekGrid>
      </PeekGrid>
    </TabsLayout>
  );
};

export default InsightsLayout;
