import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import Login from "@pages/login";
import { useSearchParams } from "react-router-dom";
import { setToken } from "@piiqtechnologies/ui-lib";
import { AllProviders } from "../utils/AllProviders";
// mock Logo component
jest.mock("@components/logo", () => ({
  __esModule: true,
  Logo: () => <div data-testid="mock-logo" />,
}));

// mock LoginButton component
jest.mock("@components/login", () => ({
  __esModule: true,
  LoginButton: () => <div data-testid="mock-login-button" />,
}));

const mockNavigate = jest.fn();
// mock useNavigate and useSearchParams hooks
jest.mock("react-router-dom", () => ({
  __esModule: true,
  useNavigate: jest.fn(() => mockNavigate),
  useSearchParams: jest.fn(() => [
    {
      get: jest.fn(),
    },
  ]),
}));

// mock setToken function
jest.mock("@piiqtechnologies/ui-lib", () => ({
  __esModule: true,
  setToken: jest.fn(),
}));

describe("Login", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render Logo component", () => {
    const { getByTestId } = render(<Login />, { wrapper: AllProviders });
    expect(getByTestId("mock-logo")).toBeInTheDocument();
  });

  it("should render LoginButton component", () => {
    const { getByTestId } = render(<Login />, { wrapper: AllProviders });
    expect(getByTestId("mock-login-button")).toBeInTheDocument();
  });

  describe("when token is present in query params", () => {
    beforeEach(() => {
      (useSearchParams as jest.Mock).mockImplementation(() => [
        {
          get: jest.fn(() => "token"),
        },
      ]);
    });
    it("should navigate to sideloader route", () => {
      render(<Login />, { wrapper: AllProviders });
      expect(mockNavigate).toHaveBeenCalledWith("/sideloader", { replace: true });
    });
    it("should call setToken function", () => {
      render(<Login />, { wrapper: AllProviders });
      expect(setToken).toHaveBeenCalledWith("token");
    });
  });

  describe("when token is not present in query params", () => {
    it("should not navigate to sideloader route", () => {
      (useSearchParams as jest.Mock).mockImplementation(() => [
        {
          get: jest.fn(() => null),
        },
      ]);
      render(<Login />, { wrapper: AllProviders });
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });
});
