{"extends": "@piiqtechnologies/tsconfig/app.json", "compilerOptions": {"baseUrl": ".", "paths": {"@pages/*": ["./src/pages/*"], "@pages": ["./src/pages/index"], "@components/*": ["./src/components/*"], "@components": ["./src/components/index"], "@common-components/*": ["./src/components/common/*"], "@utils": ["./src/utils/index"], "@layouts": ["./src/layouts/index"], "@layouts/*": ["./src/layouts/*"], "@constants/*": ["./src/constants/*"], "@stores/*": ["./src/stores/*"], "@utils/*": ["./src/utils/*"], "@fixtures/*": ["./tests/unit/__fixtures__/*"], "@testUtils/*": ["./tests/unit/utils/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "build"]}