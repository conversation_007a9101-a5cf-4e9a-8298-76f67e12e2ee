{"name": "insights-dashboard", "version": "1.0.0", "description": "Insights Dashboard", "scripts": {"dev": "webpack serve --mode development --port 3004 --progress --hot --config config/webpack.config.development.mjs", "build": "webpack --mode production --config config/webpack.config.production.mjs", "lint": "eslint '{src,tests}/**/*.{ts,tsx}' --quiet --fix", "type:check": "tsc --pretty --noEmit", "test": "yarn test:unit && yarn test:integration", "test:unit": "jest --config ./jest-unit.config.mjs", "test:unit:watch": "jest --config ./jest-unit.config.mjs --watch", "test:integration": "jest --config ./jest-integration.config.mjs", "test:integration:watch": "jest --config ./jest-integration.config.mjs --watch", "deploy": "bash ../../infra/deploy.sh", "update:env": "bash  ../../infra/fetch-aws-parameters.sh \"web/insights-dashboard\" \".env\""}, "license": "ISC", "devDependencies": {"@piiqtechnologies/jest-config": "workspace:*", "@piiqtechnologies/tsconfig": "workspace:*", "@piiqtechnologies/webpack-config": "workspace:*", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "@types/react-places-autocomplete": "^7.2.14", "eslint-config-peek": "workspace:*", "jest": "^29.6.0", "tslib": "^2.3.1", "typescript": "^5.1.3", "webpack": "^5.88.0"}, "dependencies": {"@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@mui/material": "5.10.15", "@piiqtechnologies/svg-icons": "workspace:*", "@piiqtechnologies/ui-components": "workspace:*", "@piiqtechnologies/ui-lib": "workspace:*", "@vis.gl/react-google-maps": "^1.5.3", "formik": "^2.2.9", "react": "^17.0.2", "react-dom": "^17.0.2", "react-intl": "^6.4.4", "react-places-autocomplete": "^7.3.0", "react-router-dom": "^6.2.1", "yup": "^0.32.11"}}