import Analytics, { AnalyticsInstance } from "analytics";
import mixpanelPlugin from "@analytics/mixpanel";
import { analyticsPlugin as peekAnalyticsPlugin } from "@piiqtechnologies/ui-lib";
import * as Sentry from "@sentry/react";

const analyticsTools = process.env.PEEK_APP_ANALYTICS_TOOLS || "";

export const analytics: AnalyticsInstance = Analytics({
  app: "dashboard",
  debug: false,
  plugins: [
    mixpanelPlugin({
      token: process.env.PEEK_APP_MIXPANEL_TOKEN,
      enabled: analyticsTools.includes("MIXPANEL"),
    }),
    peekAnalyticsPlugin({
      appId: process.env.PEEK_APP_ANALYTICS_APP_ID,
      enabled: analyticsTools.includes("PEEK"),
    }),
  ],
});

analytics.ready(() => {
  const sessionId = analytics.getState("context.sessionId");
  if (sessionId) {
    Sentry.setTag("session_id", sessionId as string);
  }
});

export default analytics;
