import { Search } from "@piiqtechnologies/svg-icons";
import {
  FieldProps,
  PeekAutocomplete,
  PeekBox,
  PeekDivider,
  PeekGrid,
  PeekInput,
  PeekInputAdornment,
  PeekListItem,
  PeekLoader,
  PeekSelectItem,
  PeekTypography,
  useTheme,
} from "@piiqtechnologies/ui-components";
import {
  Address,
  ScanRequest,
  ScanRequestStatus,
  Space,
  SpaceType,
  useDebounce,
  useError,
  useGoogleMapApi,
  useSpaces,
} from "@piiqtechnologies/ui-lib";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useIntl } from "react-intl";
import { AddNewOption } from "./AddNewOption";
import { SelectedSpacesList } from "./SelectedSpacesList";
import { AddCustomOptionForm } from "./add-custom-option";
import messages from "./messages.i18n";

export interface SpaceSelectorProps extends FieldProps<string> {
  placeholder: string;
  label: string;
}

export interface SpaceOption {
  id: string;
  name: string;
  unit: string;
  type: string;
  isComplete?: boolean;
  tourCapturedDate?: Date;
  scanRequests: ScanRequest[];
  bedrooms?: string;
  bathrooms?: string;
  building: {
    address: Address & { name: string; description: string };
  };
  floorPlan?: {
    name: string;
  };
}

export const SpaceSelector = ({ placeholder, label, form, field }: SpaceSelectorProps) => {
  const intl = useIntl();
  const theme = useTheme();
  const inputRef = useRef(null);

  const [showAddCustomSpaceForm, setShowAddCustomSpaceForm] = useState(false);
  const [selectedSpacesOptions, setSelectedSpacesOptions] = useState<SpaceOption[]>([]);

  const { setError } = useError();

  const [searchKey, setSearchKey] = useState<string>("");
  const debouncedSearchKey = useDebounce(searchKey, 700);

  const [getSpaces, spacesResp, spacesLoading, spacesError] = useSpaces();
  useGoogleMapApi();

  const handleCloseAddCustomSpaceForm = () => {
    setShowAddCustomSpaceForm(!showAddCustomSpaceForm);
  };

  const handleAddNewLabel = () => {
    if (inputRef.current) {
      // @ts-ignore
      inputRef.current.blur();
      handleCloseAddCustomSpaceForm();
    }
  };

  useEffect(() => {
    if (spacesError) {
      setError(spacesError?.message);
    }
  }, [spacesError]);

  useEffect(() => {
    if (form.values.community?._id) {
      getSpaces({
        search: debouncedSearchKey || undefined,
        include: "scanRequests",
        projection: "unit,building,isComplete,tourCapturedDate,type,floorPlan",
        community: {
          _id: form.values?.community?._id,
        },
      });
    }
  }, [form.values.community?._id, debouncedSearchKey]);

  const spacesOptions = useMemo(() => {
    const spaceList: PeekSelectItem[] = [
      ...((spacesResp?.data?.length &&
        spacesResp?.data
          .map(({ _id, unit, type, building, scanRequests, isComplete, tourCapturedDate, floorPlan }: Space) => ({
            id: _id,
            type,
            unit,
            building: {
              address: building.address,
            },
            isComplete,
            tourCapturedDate,
            scanRequests,
            floorPlan,
            name: `${unit} (${[building.address?.street, building.address?.postalCode].filter(Boolean).join(", ")})`,
          }))
          .sort((a, b) => (a.type === SpaceType.Unit && b.type !== SpaceType.Unit ? -1 : 1))) ||
        []),
      {
        id: "",
        type: "",
        unit: "",
        building: {
          address: "",
        },
        scanRequests: [{ status: "" }],
        name: "",
        floorPlan: {
          name: "",
        },
      },
    ];

    return spaceList;
  }, [spacesResp?.data]);

  useEffect(() => {
    const spacesPreSelected = spacesResp?.data
      ?.filter(
        (space) =>
          field.value?.includes(space._id) &&
          !space?.isComplete &&
          !isRequestSubmitted(space) &&
          !selectedSpacesOptions.some((spaceValue: SpaceOption) => spaceValue?.name === space?.name),
      )
      ?.map((space) => ({
        id: space._id,
        scanRequests: space?.scanRequests,
        isComplete: space?.isComplete,
        name: `${space?.unit} - ${space?.building?.address?.street}, ${space?.building?.address?.postalCode}`,
      }));

    if (spacesPreSelected?.length) {
      setSelectedSpacesOptions([...selectedSpacesOptions, ...spacesPreSelected]);
    }
  }, [spacesOptions, selectedSpacesOptions]);

  const handleSpaceSelection = (_event: any, space: SpaceOption) => {
    if (space?.id) {
      setSearchKey("");
      !selectedSpacesOptions.some((spaceValue: SpaceOption) => spaceValue?.name === space?.name) &&
        setSelectedSpacesOptions([...selectedSpacesOptions, space]);
    }
  };

  const handleSpaceDeselection = (space: SpaceOption) => {
    setSelectedSpacesOptions(selectedSpacesOptions.filter((option) => option?.id !== space?.id));
  };

  const extractIds = (selectedSpaces: SpaceOption[]) => {
    return selectedSpaces?.map((space: SpaceOption) => ({ _id: space?.id }));
  };

  const isRequestSubmitted = (spaceOption: SpaceOption) => {
    if (
      spaceOption.scanRequests?.some(
        (scanRequest: ScanRequest) =>
          scanRequest.status === ScanRequestStatus.REQUESTED || scanRequest.status === ScanRequestStatus.SCHEDULED,
      )
    )
      return true;
  };

  const isOptionSelected = (spaceOption: SpaceOption) => {
    if (selectedSpacesOptions.some((selectedSpaceOption: SpaceOption) => selectedSpaceOption.id === spaceOption.id))
      return true;
  };

  useEffect(() => {
    setSearchKey("");
    setShowAddCustomSpaceForm(false);
  }, [form.values?.spaceType, form.values?.community?._id]);

  useEffect(() => {
    if (selectedSpacesOptions?.length) {
      setSelectedSpacesOptions([]);
      //@ts-ignore
      form.setFieldValue(field.name, []);
    }
  }, [form.values?.community?._id]);

  useEffect(() => {
    if (selectedSpacesOptions?.length) {
      // @ts-ignore
      form.setFieldValue(field.name, extractIds(selectedSpacesOptions));
    }
  }, [selectedSpacesOptions]);

  return (
    <PeekGrid container direction="column" pl={theme.spacing(2)}>
      <>
        <PeekTypography marginBottom={theme.spacing(-1)} marginTop={theme.spacing(1.5)}>
          {label}
        </PeekTypography>
        {/* {spacesLoading ? (
          <PeekBox
            sx={{
              mt: theme.spacing(1.4),
              pt: theme.spacing(0.4),
              width: "100%",
              height: theme.spacing(2.5),
              img: {
                height: "inherit",
                width: "auto",
              },
            }}
          >
            <PeekLoader />
          </PeekBox>
        ) : ( */}
        <PeekAutocomplete
          loading={spacesLoading}
          freeSolo={false}
          clearIcon={null}
          popupIcon={null}
          onChange={handleSpaceSelection}
          groupBy={(option) => option.type}
          options={spacesOptions || []}
          renderGroup={(params) => (
            <PeekListItem sx={{ display: "block" }} key={Math.random()}>
              <PeekTypography
                variant={"body2"}
                fontWeight={theme.typography.fontWeightBold}
                color={theme.palette.text.disabled}
                ml={0.5}
                textTransform={"uppercase"}
              >
                {params.group}
              </PeekTypography>
              <PeekTypography>{params.children}</PeekTypography>
            </PeekListItem>
          )}
          disabled={!form?.values?.community?._id}
          filterOptions={(option: SpaceOption) => option}
          getOptionLabel={(value: SpaceOption) =>
            (!!value.id &&
              `${value.name} ${isRequestSubmitted(value) ? `- ${intl.formatMessage(messages.requestSubmittedNote)}` : ""}`) ||
            ""
          }
          sx={{ ".MuiAutocomplete-input": { pl: "4px !important" } }}
          value={spacesOptions?.find((option: SpaceOption) => option.id === field.value)}
          getOptionDisabled={(value: SpaceOption) => isOptionSelected(value) || isRequestSubmitted(value)}
          isOptionEqualToValue={(option: SpaceOption, value: SpaceOption) => option.id === value.id}
          onAddNew={() => handleAddNewLabel()}
          addNewLabelSize={20}
          addNewLabel={<AddNewOption isCommunitySelected={!!form.values.community} />}
          renderInput={(params: any) => {
            const paramsCustomValue = { ...params, inputProps: { ...params.inputProps, value: searchKey } };
            return (
              <PeekInput
                form={form}
                field={{ ...field }}
                onChange={(event) => {
                  setSearchKey(event.target.value);
                }}
                {...paramsCustomValue}
                InputProps={{
                  ...paramsCustomValue.InputProps,
                  inputRef: inputRef,
                  placeholder: !form?.values?.community?._id
                    ? intl.formatMessage(messages.addNewOptionCommunity)
                    : placeholder,
                  startAdornment: (
                    <PeekInputAdornment position="end">
                      <PeekTypography variant="h3" color="text" display="flex">
                        <Search color={theme.palette.grey[300]} />
                      </PeekTypography>
                    </PeekInputAdornment>
                  ),
                }}
              />
            );
          }}
        />
        {/* )} */}
      </>
      {showAddCustomSpaceForm && (
        <AddCustomOptionForm
          handleClose={() => handleCloseAddCustomSpaceForm()}
          handleSpaceSelection={(space) => handleSpaceSelection(undefined, space)}
          community={form.values?.community}
        />
      )}
      <PeekDivider orientation="horizontal" light sx={{ my: theme.spacing(1.15) }} />
      <PeekTypography fontWeight={theme.typography.fontWeightMedium}>
        {intl.formatMessage(messages.scanRequestedListTitle)}
      </PeekTypography>
      {!selectedSpacesOptions?.length ? (
        <PeekTypography>{intl.formatMessage(messages.scanRequestedListNoScan)}</PeekTypography>
      ) : (
        <SelectedSpacesList
          selectedSpacesOptions={selectedSpacesOptions}
          handleSpaceDeselection={(space) => handleSpaceDeselection(space)}
        />
      )}
    </PeekGrid>
  );
};

export default SpaceSelector;
