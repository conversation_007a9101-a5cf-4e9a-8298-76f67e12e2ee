import React, { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { MultiColumnFormFields } from "@components/common/form/FormFields";

import { useError, useLoader, useCreateSpace, Community } from "@piiqtechnologies/ui-lib";
import {
  PeekButton,
  PeekTypography,
  PeekStack,
  useTheme,
  PeekPaper,
  PeekIconButton,
} from "@piiqtechnologies/ui-components";

import { Formik, Form } from "formik";
import messages from "./messages.i18n";
import { AddCustomOptionFormValidationSchema, AddCustomOptionFormFields } from "./AddCustomOptionFormConfig";
import { SpaceOption } from "../SpaceSelector";
import { X } from "@piiqtechnologies/svg-icons";

interface AddCustomOptionFormProps {
  community: Community;
  handleClose: () => void;
  handleSpaceSelection: (space: SpaceOption) => void;
}

interface HandleAddCustomOptionFormProps
  extends Pick<SpaceOption, "name" | "building" | "floorPlan" | "bathrooms" | "bedrooms"> {
  isUnitForm: boolean;
}

export const AddCustomOptionForm = ({ handleSpaceSelection, community, handleClose }: AddCustomOptionFormProps) => {
  const intl = useIntl();
  const theme = useTheme();

  const [isUnitForm, setIsUnitForm] = useState(false);

  const { setError } = useError();
  const { setLoader } = useLoader();

  const [createSpace, spaceCreated, createSpaceInProgress, createSpaceError] = useCreateSpace();

  useEffect(() => {
    setLoader(createSpaceInProgress, "createSpaceInProgress");
    return () => setLoader(false, "createSpaceInProgress");
  }, [createSpaceInProgress]);

  useEffect(() => {
    if (createSpaceError) {
      if (createSpaceError.message.includes("E11000")) {
        setError(intl.formatMessage(messages.duplicatedSpaceError));
        return;
      }

      setError(createSpaceError?.message);
    }
  }, [createSpaceError]);

  const handleAddCustomOptionForm = (values: HandleAddCustomOptionFormProps) => {
    const { name, building, floorPlan } = values;
    const unitLayout = {
      bedrooms: isUnitForm && (values.bedrooms || 0),
      bathrooms: values.bathrooms,
    };

    const spacePayload = {
      community: {
        _id: community?._id,
      },
      isVisible: true,
      isComplete: false,
      type: isUnitForm ? "unit" : "amenity",
      unit: name,
      floorPlan: {
        name: floorPlan?.name,
      },
      building: {
        address: {
          longitude: building?.address?.longitude,
          latitude: building?.address?.latitude,
          placeId: building?.address?.placeId,
          postalCode: building?.address?.postalCode,
          city: building?.address?.city,
          state: building?.address?.state,
          country: building?.address?.country,
          geocodeData: building?.address?.geocodeData,
          street:
            building?.address?.street < building?.address?.name ? building?.address?.street : building?.address?.name,
          street2: building?.address?.description || building?.address?.street2,
        },
      },
      ...(isUnitForm && unitLayout),
    };

    createSpace(spacePayload);
  };

  useEffect(() => {
    if (spaceCreated) {
      handleClose();
      handleSpaceSelection({
        id: spaceCreated._id,
        unit: spaceCreated.unit,
        building: {
          address: spaceCreated.building.address,
        },
        scanRequests: [],
        type: isUnitForm ? "unit" : "amenity",
        name: `${spaceCreated.unit} - ${[
          spaceCreated.building.address?.street,
          spaceCreated.building.address?.city,
          spaceCreated.building.address?.state,
          spaceCreated.building.address?.postalCode,
        ]
          .filter(Boolean)
          .join(", ")}`,
      });
    }
  }, [spaceCreated]);

  const AddCustomOptionFormInitialValues = {
    name: "",
    bedrooms: "",
    isUnitForm: true,
    bathrooms: "",
    floorPlan: {
      name: "",
    },
    building: {
      //@ts-ignore
      address:
        (!!community?.address && {
          ...community?.address,
          name:
            community.address?.street2 ||
            [community.address?.street, community.address?.city, community.address?.state, community.address?.country]
              .filter(Boolean)
              .join(", "),
        }) ||
        "",
    },
  };

  return (
    <>
      <PeekPaper
        variant="outlined"
        sx={{
          p: theme.spacing(2),
          bgcolor: theme.palette.grey[50],
          border: `1px dashed ${theme.palette.grey[200]}`,
        }}
      >
        <PeekStack direction="column">
          <PeekStack direction={"row"} alignItems={"center"} justifyContent={"space-between"} mb={1}>
            <PeekTypography variant={"h3"}>{intl.formatMessage(messages.customSpaceTitle)}</PeekTypography>
            <PeekIconButton bordered={false} onClick={handleClose}>
              <X />
            </PeekIconButton>
          </PeekStack>
          <Formik
            // @ts-ignore
            onSubmit={handleAddCustomOptionForm}
            initialValues={AddCustomOptionFormInitialValues}
            validationSchema={AddCustomOptionFormValidationSchema({ isUnitForm: isUnitForm })}
          >
            {({ values, handleSubmit }) => {
              useEffect(() => {
                setIsUnitForm(values.isUnitForm);
              }, [values.isUnitForm]);

              return (
                <Form>
                  <PeekStack direction="column" gap={1.5}>
                    <MultiColumnFormFields
                      fullWidth
                      fields={AddCustomOptionFormFields({
                        isUnitForm: values.isUnitForm,
                      })}
                    />
                    {/* @ts-ignore */}
                    <PeekButton variant="outlined" onClick={handleSubmit}>
                      {intl.formatMessage(messages.customSpaceSubmit)}
                    </PeekButton>
                  </PeekStack>
                </Form>
              );
            }}
          </Formik>
        </PeekStack>
      </PeekPaper>
    </>
  );
};

export default AddCustomOptionForm;
