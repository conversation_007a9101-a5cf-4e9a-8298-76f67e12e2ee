import { defineMessages } from "react-intl";

const messages = defineMessages({
  // Aux
  customUnitForm: {
    id: "app.scanRequests.unitList.customSpace.unit",
    defaultMessage: "Unit",
  },
  customAmenityForm: {
    id: "app.scanRequests.unitList.customSpace.amenity",
    defaultMessage: "Amenity",
  },

  // Titles
  customSpaceTitle: {
    id: "app.scanRequests.unitList.customSpace.unit.title",
    defaultMessage: "Create Custom Space",
  },
  customSpaceSubmit: {
    id: "app.scanRequests.customSpace.submit",
    defaultMessage: "Create",
  },

  // Placeholder
  customSpaceTypePlaceholder: {
    id: "app.scanRequests.customSpace.type.placeholder",
    defaultMessage: "Space Type:",
  },
  customSpacePlaceholder: {
    id: "app.scanRequests.customSpace.name.placeholder",
    defaultMessage: "Name",
  },
  customSpaceAddressPlaceholder: {
    id: "app.scanRequests.customSpace.address.placeholder",
    defaultMessage: "Select Building",
  },
  customUnitBedroomsPlaceholder: {
    id: "app.scanRequests.customUnit.bedrooms.placeholder",
    defaultMessage: "Bedrooms",
  },
  customUnitBathroomsPlaceholder: {
    id: "app.scanRequests.customUnit.bathrooms.placeholder",
    defaultMessage: "Bathrooms",
  },
  customFloorplanNamePlaceholder: {
    id: "app.scanRequests.customUnit.floorplane.name.placeholder",
    defaultMessage: "Floorplan name",
  },

  // Errors
  customUnitLayoutInvalid: {
    id: "app.scanRequests.customUnit.layout.error",
    defaultMessage: "Invalid number",
  },
  customSpaceError: {
    id: "app.scanRequests.customSpace.name.error",
    defaultMessage: "Space identifier is required",
  },
  customAddressError: {
    id: "app.scanRequests.customSpace.address.error",
    defaultMessage: "Address is required",
  },
  customLayoutError: {
    id: "app.scanRequests.customSpace.form.error",
    defaultMessage: "BR/BA is required",
  },
  addressError: {
    id: "app.scanRequests.unitList.customUnit.addressError",
    defaultMessage: "Please, provide proper Address",
  },
  noCustomAddressError: {
    id: "app.scanRequests.customSpace.noAddress.error",
    defaultMessage: "No Addresses available for this Community",
  },
  duplicatedSpaceError: {
    id: "app.scanRequests.customSpace.duplicatedSpace.error",
    defaultMessage:
      "It looks like there is a space with the same name & address already in the Peek dashboard. Try a different name or reach <NAME_EMAIL> for further assistance",
  },
  customFloorplanNameError: {
    id: "app.scanRequests.customSpace.floorplane.name.error",
    defaultMessage: "Floorplan name is required",
  },
});

export default messages;
