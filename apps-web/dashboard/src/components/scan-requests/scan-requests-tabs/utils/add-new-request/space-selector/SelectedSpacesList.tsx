import React from "react";

import { SpaceOption } from "./SpaceSelector";

import { Peek<PERSON><PERSON>, PeekPaper, PeekStack, PeekTypography, useTheme } from "@piiqtechnologies/ui-components";
import RemoveScanRequestButton from "./RemoveScanRequestButton";
import { format } from "date-fns";
import { ScanRequest } from "@piiqtechnologies/ui-lib";

import { useIntl } from "react-intl";
import messages from "./messages.i18n";

interface SelectedSpacesProps {
  selectedSpacesOptions: SpaceOption[];
  handleSpaceDeselection: (space: SpaceOption) => void;
}

export const SelectedSpacesList: React.FC<SelectedSpacesProps> = ({
  handleSpaceDeselection,
  selectedSpacesOptions,
}) => {
  const intl = useIntl();
  const theme = useTheme();

  const findLatestScannedAt = (space: SpaceOption): string | null => {
    let latestScannedAt: Date | null = null;

    if (space.isComplete) {
      latestScannedAt = new Date(space.tourCapturedDate);
    }

    space.scanRequests?.forEach((item: ScanRequest) => {
      if (item.scannedAt) {
        const scannedAtDate = new Date(item.scannedAt);
        if (!latestScannedAt || scannedAtDate > latestScannedAt) {
          latestScannedAt = scannedAtDate;
        }
      }
    });

    return latestScannedAt ? format(new Date(latestScannedAt), "MM/dd/yy") : null;
  };

  return (
    <PeekStack
      spacing={theme.spacing(1.25)}
      mt={theme.spacing(1)}
      maxHeight={480}
      maxWidth={"100%"}
      mr={theme.spacing(-1.5)}
      pr={theme.spacing(1)}
      sx={{ overflowY: "scroll" }}
    >
      {selectedSpacesOptions.map((space) => {
        const latestScannedAt = findLatestScannedAt(space);
        return (
          <PeekPaper
            variant="outlined"
            key={space?.id}
            sx={{
              textAlign: "left",
              alignItems: "center",
              p: theme.spacing(0.7),
              backgroundColor: "grey.50",
              borderRadius: theme.spacing(0.5),
            }}
          >
            <PeekStack direction="row" justifyContent="space-between" alignItems="center">
              <PeekTypography
                variant="body1"
                p={theme.spacing(0.8)}
                sx={{
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  maxWidth: latestScannedAt ? "50%" : "80%",
                }}
              >
                {space?.name}
              </PeekTypography>
              <PeekStack direction="row" alignItems="center">
                {latestScannedAt && (
                  <PeekChip
                    size="small"
                    sx={{
                      backgroundColor: `${theme.palette.primary.contrastText} !important`,
                      height: "fit-content",
                    }}
                    label={intl.formatMessage(messages.scannedAtChipOnSpaceSelected, { date: latestScannedAt })}
                  />
                )}
                <RemoveScanRequestButton onClick={() => handleSpaceDeselection(space)} />
              </PeekStack>
            </PeekStack>
          </PeekPaper>
        );
      })}
    </PeekStack>
  );
};

export default SelectedSpacesList;
