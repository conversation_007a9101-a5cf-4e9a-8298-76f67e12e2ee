import { useIntl } from "react-intl";

import * as yup from "yup";
import messages from "./messages.i18n";

import { PeekInput, useTheme, PeekAddressInput } from "@piiqtechnologies/ui-components";
import { SpaceTypeSelector } from "./space-type-selector";

interface AddCustomOptionFormFieldsProps {
  isUnitForm: boolean;
}
interface AddCustomOptionFormValidationProps {
  isUnitForm: boolean;
}

export const AddCustomOptionFormValidationSchema = ({ isUnitForm }: AddCustomOptionFormValidationProps) => {
  const intl = useIntl();
  return yup.object({
    name: yup
      .string()
      .min(1, intl.formatMessage(messages.customSpaceError))
      .required(intl.formatMessage(messages.customSpaceError)),
    floorPlan: yup.object({
      name: yup.string().required(intl.formatMessage(messages.customFloorplanNameError)),
    }),
    building: yup.object({
      address: yup.object({ name: yup.string() }).test({
        message: intl.formatMessage(messages.addressError),
        test: (value) => value && value.name && value.name.length > 0,
      }),
    }),
    bedrooms: isUnitForm
      ? yup
          .number()
          .min(0, intl.formatMessage(messages.customUnitLayoutInvalid))
          .required(intl.formatMessage(messages.customLayoutError))
      : null,
    bathrooms: isUnitForm
      ? yup
          .number()
          .min(0, intl.formatMessage(messages.customUnitLayoutInvalid))
          .required(intl.formatMessage(messages.customLayoutError))
      : null,
  });
};

export const AddCustomOptionFormFields = ({ isUnitForm }: AddCustomOptionFormFieldsProps) => {
  const intl = useIntl();
  const theme = useTheme();

  const unitLayout = [
    {
      id: "bedrooms",
      columns: 6,
      label: "",
      placeholder: intl.formatMessage(messages.customUnitBedroomsPlaceholder),
      required: isUnitForm,
      style: { marginTop: theme.spacing(-1.5) },
      type: "number",
    },
    {
      id: "bathrooms",
      columns: 6,
      label: "",
      placeholder: intl.formatMessage(messages.customUnitBathroomsPlaceholder),
      required: isUnitForm,
      style: { marginTop: theme.spacing(-1.5) },
      component: PeekInput,
      type: "number",
    },
  ];

  return {
    unitDetails: [
      {
        id: "isUnitForm",
        label: "",
        placeholder: intl.formatMessage(messages.customSpaceTypePlaceholder),
        required: true,
        columns: 12,
        component: SpaceTypeSelector,
      },
      {
        id: "name",
        label: "",
        placeholder: intl.formatMessage(messages.customSpacePlaceholder),
        required: true,
        columns: 12,
      },
      {
        id: "building.address",
        label: "",
        placeholder: intl.formatMessage(messages.customSpaceAddressPlaceholder),
        required: true,
        columns: 12,
        component: PeekAddressInput,
      },
      {
        id: "floorPlan.name",
        label: "",
        placeholder: intl.formatMessage(messages.customFloorplanNamePlaceholder),
        required: true,
        columns: 12,
      },
      ...((isUnitForm && [...unitLayout]) || []),
    ],
  };
};
