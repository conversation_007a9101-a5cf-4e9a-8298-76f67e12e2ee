import { useIntl } from "react-intl";
import { messages } from "./messages.i18n";
import { DxServices, DxSyncTypes } from "@piiqtechnologies/ui-lib";

export interface SyncDetailsProps {
  dataExchange: DxSyncTypes;
  service: DxServices;
}

export const syncProspectServices = [
  DxServices.ENTRATA,
  DxServices.YARDI,
  DxServices.ANYONEHOME,
  DxServices.KNOCK,
  DxServices.FUNNEL,
  DxServices.ELISEAI,
  DxServices.RENTCAFEV2,
];

export const getSyncTypeOptions = ({ service, dataExchange }: SyncDetailsProps) => {
  const intl = useIntl();

  const syncTypeOptions = [];

  if (syncProspectServices.includes(service)) {
    syncTypeOptions.push({
      value: DxSyncTypes.Prospect,
      name: intl.formatMessage(messages.dxSettingsAdditionalSyncProspectName),
    });
  }

  if (dataExchange[DxSyncTypes.CRM]) {
    syncTypeOptions.push(
      {
        value: DxSyncTypes.SGT,
        name: intl.formatMessage(messages.dxSettingsAdditionalSyncSGTName),
      },
      {
        value: DxSyncTypes.AGT,
        name: intl.formatMessage(messages.dxSettingsAdditionalSyncTourName),
      },
    );
  }

  return syncTypeOptions;
};

export default getSyncTypeOptions;
