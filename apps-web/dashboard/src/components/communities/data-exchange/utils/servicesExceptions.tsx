import TimezoneSelector from "@components/common/form/timezone-selector";
import { PeekInputCheckbox } from "@piiqtechnologies/ui-components";
import { DxServices, DxSyncTypes } from "@piiqtechnologies/ui-lib";

interface FormatServiceFieldProps {
  name: string;
  type: string;
}

interface FormatServiceProps {
  service: DxServices | string;
}

interface FormatServiceValuesProps extends FormatServiceProps {
  values: any;
  dataExchange?: DxSyncTypes;
}

interface FormatServiceFieldRequiredProps {
  field: any;
  service: DxServices | string;
  dataExchange?: DxSyncTypes;
}

export const formatServiceName = ({ service }: FormatServiceProps) => {
  if (service === DxServices.REALPAGE) return "realpage";
  if (service === "realpage") return DxServices.REALPAGE;
  return service;
};

export const formatServiceFieldComponent = ({ name, type }: FormatServiceFieldProps) => {
  switch (type) {
    case "Boolean":
      return PeekInputCheckbox;
  }

  switch (name) {
    case "timezone":
      return TimezoneSelector;
    default:
      return;
  }
};

export const formatServiceFieldLabel = ({ service, field }: FormatServiceFieldRequiredProps) => {
  const insertSpaces = (text: string) =>
    text?.replace(/([a-z])([A-Z])/g, "$1 $2")?.replace(/^./, (str) => str.toUpperCase());

  const name = insertSpaces(field.name);

  switch (service) {
    case DxServices.RENTCAFEV2:
      if (name === "Url" || name === "Marketing Url") return `${name} (Non-US Customers)`;
    default:
      return name;
  }
};

export const formatServiceFieldRequired = ({ service, field, dataExchange }: FormatServiceFieldRequiredProps) => {
  switch (service) {
    case DxServices.ENTRATA:
      if (dataExchange[DxSyncTypes.PMS] && field.name === "leadSourceId") return false;
    default:
      return field?.required || false;
  }
};

export const hideServiceFields = ({ service }: FormatServiceProps) => {
  switch (service) {
    case DxServices.ENTRATA:
      return ["user", "pass", "addressSeparators"];

    case DxServices.YARDI:
      return ["license", "entity"];

    case DxServices.REALPAGE || formatServiceName({ service: service }):
      return ["user", "pass", "addressSeparators", "useFloorPlanNameMarketing"];

    case DxServices.ENGRAIN:
      return ["apiKey", "sightMapUrl", "unitMapUrl", "label", "name", "iconType"];

    case DxServices.KNOCK:
      return ["version"];

    case DxServices.ZILLOW:
      return ["address.country"];

    case DxServices.FUNNEL:
      return ["partnerApiKey"];

    case DxServices.ANYONEHOME:
      return ["apiKey"];

    default:
      return [];
  }
};

export const formatServiceValues = ({ service, values, dataExchange }: FormatServiceValuesProps) => {
  switch (service) {
    case DxServices.ENTRATA:
      const defaultValues = {
        user: "username",
        pass: "password",
        addressSeparators: [],
      };

      if (dataExchange[DxSyncTypes.PMS]) {
        return {
          ...values,
          ...defaultValues,
          leadSourceId: values?.leadSourceId || 0,
        };
      }

      return { ...values, ...defaultValues };

    case DxServices.YARDI: {
      const { agentName, transactionSource, ...rest } = values;
      const yardiValues = Object.fromEntries(Object.entries(rest).filter(([key]) => !key.startsWith("agentName.")));
      return {
        ...yardiValues,
        entity: "Peek",
      };
    }

    case DxServices.REALPAGE || formatServiceName({ service: service }):
      return {
        ...values,
        user: "username",
        pass: "password",
        useFloorPlanNameMarketing: false,
        addressSeparators: [],
      };

    case DxServices.ENGRAIN:
      return {
        ...values,
        sightMapUrl: "https://api.sightmap.com/v1/assets",
        unitMapUrl: "https://api.unitmap.com/v1/assets/references",
        label: "View Virtual Tour",
        name: "View Virtual Tour - Peek",
        iconType: "360_tour",
      };

    case DxServices.RENTCAFEV2:
      return {
        ...values,
        externalID: values.propertyId.toString(),
      };

    case DxServices.KNOCK:
      return { ...values, version: 2 };

    case DxServices.ZILLOW:
      return { address: { ...values.address, country: "US" } };

    case DxServices.FUNNEL:
      return {
        ...values,
      };

    default:
      return values;
  }
};
