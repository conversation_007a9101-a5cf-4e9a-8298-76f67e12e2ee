import React, { useEffect } from "react";
import "./index.css";
import "@piiqtechnologies/ui-lib/lib/index.css";
import { ThemeProvider } from "@piiqtechnologies/ui-components";
import * as Sentry from "@sentry/react";
import { IntlProvider } from "react-intl";
import { BrowserRouter } from "react-router-dom";
import GlobalStyles from "@mui/material/GlobalStyles";
import { ANALYTICS_DB_NAME, ErrorProvider, LoaderProvider, NotificationProvider } from "@piiqtechnologies/ui-lib";
import messages from "./lang/en.json";
import AppRoutes from "./Routes";
import theme from "./theme";
import AuthContextProvider from "@stores/AuthContext";
import { ERROR_CODES } from "@components/error";
import { Error } from "./pages/error";

const initLocale = "en";

const App = () => {
  useEffect(() => {
    return () => {
      indexedDB.deleteDatabase(ANALYTICS_DB_NAME);
    };
  }, []);

  return (
    <BrowserRouter>
      <ErrorProvider>
        <LoaderProvider>
          <NotificationProvider>
            <ThemeProvider theme={theme}>
              <GlobalStyles
                styles={{
                  "*::-webkit-scrollbar": {
                    backgroundColor: "transparent",
                    width: 6,
                  },
                  "*::-webkit-scrollbar-track": {
                    boxShadow: "none !important",
                    backgroundColor: "transparent",
                  },
                  "*::-webkit-scrollbar-thumb": {
                    backgroundColor: theme.palette.grey[200],
                    borderRadius: theme.spacing(10),
                  },
                }}
              />
              <IntlProvider messages={messages} locale={initLocale}>
                <AuthContextProvider>
                  <AppRoutes />
                </AuthContextProvider>
              </IntlProvider>
            </ThemeProvider>
          </NotificationProvider>
        </LoaderProvider>
      </ErrorProvider>
    </BrowserRouter>
  );
};

export default Sentry.withErrorBoundary(App, {
  fallback: <Error errorCode={ERROR_CODES.INTERNAL_ERROR} />,
});
