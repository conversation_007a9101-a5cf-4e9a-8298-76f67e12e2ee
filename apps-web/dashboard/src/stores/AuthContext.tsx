import React, { createContext, useContext, useEffect, useMemo, useState } from "react";
import {
  LoginResponse,
  PolicyContextProvider,
  setToken,
  useError,
  useUser,
  useLoader,
  User,
  useAnalytics,
  parseToken,
} from "@piiqtechnologies/ui-lib";
import { useLocation, useNavigate } from "react-router-dom";
import { PolicyActions, PolicyGroups } from "@constants/Policies";
import { isBetaFeatureEnabled } from "@utils";
import {
  generateAllowedPolicies,
  getUserContext,
  isAdminUser,
  getRedirectPathAfterLogin,
  storeUserContext,
  UserContext,
} from "./utils";
import * as Sentry from "@sentry/react";

type AuthContextType = {
  isAdmin: boolean;
  onLogout: () => void;
  proxyUser?: User;
  user: User;
  sso: boolean;
  onLogin: (loggedInUser: LoginResponse, sso: boolean) => void;
  syncUserContext: (user: Partial<User>) => void;
  reloadCurrentUser: () => void;
  updateDenyPolicies: (policy: string) => void;
};

export const AuthContext = createContext<AuthContextType>(null!);

const AuthContextProvider: React.FC = ({ children }) => {
  const [userContext, setUserContext] = useState<UserContext | null>(getUserContext());
  const [getUser, user, getUserInProgress, getUserError] = useUser();
  const [shouldNavigate, setShouldNavigate] = useState(false);
  const [denyPolicies, setDenyPolicies] = useState([]);

  const analytics = useAnalytics();
  const navigate = useNavigate();
  const { state: locationState } = useLocation();
  const { setError } = useError();
  const { setLoader } = useLoader();

  useEffect(() => {
    setLoader(getUserInProgress, "getUserInProgress");
    return () => setLoader(false, "getUserInProgress");
  }, [getUserInProgress]);

  useEffect(() => {
    if (getUserError) {
      setError(getUserError?.message);
    }
  }, [getUserError]);

  const isAdmin = useMemo(() => isAdminUser(userContext), [userContext]);
  const redirectPathAfterLogin = useMemo(() => getRedirectPathAfterLogin(user), [user]);

  const onLogin = (loggedInUser: LoginResponse, sso: boolean) => {
    setShouldNavigate(true);
    const { token } = loggedInUser;
    const { proxyUser } = parseToken(token);
    getUser({ userId: "__current" });
    setToken(token);
    setUserContext((_userContext) => ({ ..._userContext, sso, proxyUser }));
  };

  useEffect(() => {
    if (userContext?.user?._id) {
      analytics.identify(userContext.user._id, {
        name: userContext.user.name,
        role: userContext.user.role?.name,
        organization: userContext.user.organization?.name,
        organizationId: userContext.user.organization?._id,
      });

      Sentry.setUser({
        id: userContext.user._id,
      });
    }
  }, [userContext]);

  useEffect(() => {
    if (!user) return;

    setUserContext((_userContext) => {
      storeUserContext({ ..._userContext, user });
      return { ..._userContext, user };
    });

    let redirectPath = redirectPathAfterLogin;
    if (locationState) {
      const { redirectTo } = locationState;
      redirectPath = `${redirectTo.pathname}${redirectTo.search}`;
    }

    shouldNavigate && navigate(redirectPath, { replace: true });
  }, [user]);

  const onLogout = () => {
    setToken(null);
    sessionStorage.clear();
    setUserContext(null);

    Sentry.configureScope((scope) => {
      scope.setUser(null);
    });
  };

  const reloadCurrentUser = () => {
    setShouldNavigate(false);
    getUser({ userId: "__current" });
  };

  const syncUserContext = (partialUser: Partial<User>) => {
    setUserContext((_userContext) => {
      const newUserContext = { ..._userContext, user: { ..._userContext.user, ...partialUser } };
      storeUserContext(newUserContext);
      return newUserContext;
    });
  };

  const updateDenyPolicies = (policy: PolicyActions) => {
    setDenyPolicies([policy, ...denyPolicies]);
  };

  const userFeatures = useMemo(() => userContext?.user?.features || [], [userContext]);

  const featureGatedPolicies = useMemo(() => {
    // check if user has access to feature if its beta feature
    return PolicyGroups.FEATURE_GATING.filter((policy) => {
      if (userContext?.user && isBetaFeatureEnabled(policy)) {
        return userContext?.user?.allow?.includes(`peek:users:${userContext?.user?._id}::${policy}`);
      }
      return true;
    });
  }, [userContext?.user]);

  const allowedPolicies = useMemo(() => {
    return generateAllowedPolicies(
      userContext?.user?.role?.alias || "",
      userFeatures,
      featureGatedPolicies,
      userContext?.user?.deny || [],
    );
  }, [userContext?.user?.role?.alias, userFeatures, featureGatedPolicies, userContext?.user?.deny]);

  return (
    <AuthContext.Provider
      value={{
        user: userContext?.user,
        proxyUser: userContext?.proxyUser,
        sso: userContext?.sso,
        onLogin,
        isAdmin,
        onLogout,
        syncUserContext,
        reloadCurrentUser,
        updateDenyPolicies,
      }}
    >
      {/* TODO rework on policy */}
      {/* <PolicyContextProvider allow={userContext?.user?.allow || []} deny={userContext?.user?.deny || []}> */}
      <PolicyContextProvider allow={allowedPolicies} deny={[...denyPolicies, ...(userContext?.user?.deny || [])]}>
        {children}
      </PolicyContextProvider>
    </AuthContext.Provider>
  );
};

export function useAuthContext() {
  return useContext(AuthContext);
}

export default AuthContextProvider;
