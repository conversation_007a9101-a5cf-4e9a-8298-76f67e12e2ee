{"name": "dashboard", "version": "1.0.0", "description": "A Peek dashboard application that helps organisation users likes agents, organisation admin and peek support team to view/manage spaces virtual tours, analytics about virtual tours and prospects, accounts management, SGT management.", "scripts": {"dev": "webpack serve --mode development --port 3000 --progress --hot --config config/webpack.config.development.mjs", "build": "webpack --mode production --config config/webpack.config.production.mjs", "lint": "eslint '{src,tests}/**/*.{ts,tsx}' --quiet --fix", "type:check": "tsc --pretty --noEmit", "extract-i18n": "formatjs extract 'src/**/*.i18n.ts*' --ignore='**/*.d.ts' --out-file src/lang/en.json --id-interpolation-pattern '[sha512:contenthash:base64:6]' --format simple", "test": "yarn test:unit && yarn test:integration", "test:unit": "jest --config ./jest-unit.config.mjs", "test:unit:watch": "jest --config ./jest-unit.config.mjs --watch", "test:integration": "jest --config ./jest-integration.config.mjs", "test:integration:watch": "jest --config ./jest-integration.config.mjs --watch", "deploy": "bash ../../infra/deploy.sh", "update:env": "bash  ../../infra/fetch-aws-parameters.sh \"web/dashboard\" \".env\""}, "license": "ISC", "devDependencies": {"@faker-js/faker": "^8.0.2", "@formatjs/cli": "^5.0.2", "@piiqtechnologies/jest-config": "workspace:*", "@piiqtechnologies/tsconfig": "workspace:*", "@piiqtechnologies/webpack-config": "workspace:*", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/dot-object": "^2.1.2", "@types/faker": "^6.6.9", "@types/papaparse": "^5.3.7", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.11", "eslint-config-peek": "workspace:*", "jest": "^29.6.0", "tslib": "^2.3.1", "typescript": "^5.1.3", "webpack": "^5.88.2"}, "dependencies": {"@analytics/mixpanel": "^0.4.0", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@mui/material": "5.10.15", "@piiqtechnologies/svg-icons": "workspace:*", "@piiqtechnologies/ui-components": "workspace:*", "@piiqtechnologies/ui-lib": "workspace:*", "@sentry/react": "^7.92.0", "analytics": "^0.8.1", "date-fns": "^2.28.0", "date-fns-tz": "^2.0.0", "dayjs": "^1.10.7", "dompurify": "^3.0.4", "dot-object": "^2.1.4", "formik": "^2.2.9", "mui-nested-menu": "^3.4.0", "papaparse": "^5.4.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-intl": "^6.4.4", "react-router-dom": "^6.2.1", "yup": "^0.32.11"}}